#!/usr/bin/env python3
"""
Demonstration script for testing the real long_task tool.

This script shows how to:
1. Start an MCP server with the long_task tool
2. Connect to it and call the tool with progress reporting
3. Test the functionality with loaded context

Usage:
    # Terminal 1: Start the MCP server
    cd gaia/gaia_ceto/proto_mcp/long_task_test
    python minimal_mcp_server.py
    
    # Terminal 2: Run this test script
    cd gaia/gaia_ceto/ceto_v002
    python test_real_long_task.py
"""

import asyncio
import json
import os
import tempfile
import time
from pathlib import Path

# Try to import FastMCP for real tool testing
try:
    from fastmcp import Client
    FASTMCP_AVAILABLE = True
    print("✅ FastMCP available for real tool testing")
except ImportError:
    FASTMCP_AVAILABLE = False
    print("❌ FastMCP not available - install with: pip install fastmcp")

from gaia.gaia_ceto.ceto_v002.chat_term import ChatTerminal


async def test_real_long_task_tool():
    """Test the actual long_task tool with real MCP server."""
    if not FASTMCP_AVAILABLE:
        print("❌ Cannot test real tool - FastMCP not available")
        return False
    
    print("\n" + "="*70)
    print("TESTING REAL LONG_TASK TOOL")
    print("="*70)
    
    # Create temporary directories
    temp_dir = tempfile.mkdtemp()
    context_dir = tempfile.mkdtemp()
    
    try:
        # Create test context data
        test_context = {
            "type": "companies",
            "title": "Real Tool Test Companies",
            "description": "Companies for testing real long_task tool functionality",
            "data": [
                {
                    "id": 1,
                    "name": "Alpha Technologies",
                    "industry": "Software",
                    "founded": 2020,
                    "employees": 85,
                    "revenue": "$5.2M",
                    "description": "AI-powered software solutions"
                },
                {
                    "id": 2,
                    "name": "Beta Manufacturing",
                    "industry": "Industrial",
                    "founded": 2018,
                    "employees": 150,
                    "revenue": "$12.8M",
                    "description": "Advanced manufacturing processes"
                },
                {
                    "id": 3,
                    "name": "Gamma Healthcare",
                    "industry": "Healthcare",
                    "founded": 2019,
                    "employees": 120,
                    "revenue": "$8.9M",
                    "description": "Digital health platforms"
                }
            ]
        }
        
        # Save context to file
        context_file = os.path.join(context_dir, "real_test_companies.json")
        with open(context_file, 'w', encoding='utf-8') as f:
            json.dump(test_context, f, indent=2)
        
        print(f"📁 Created test context file: {context_file}")
        print(f"📊 Context contains {len(test_context['data'])} companies")
        
        # Create ChatTerminal instance
        terminal = ChatTerminal(
            storage_dir=temp_dir,
            llm_provider="mock"
        )
        terminal.context_dir = context_dir
        
        # Load context
        print("\n🔄 Loading context...")
        terminal.cmd_load_context("real_test_companies.json")
        
        print("✅ Context loaded successfully")
        print(f"📝 Conversation title: {terminal.chat_manager.active_conversation.title}")
        
        # Test direct connection to MCP server
        print("\n🔗 Testing direct connection to MCP server...")
        
        try:
            # Try to connect to the MCP server
            server_url = "http://localhost:9000"
            
            print(f"🌐 Connecting to MCP server at {server_url}...")
            
            async with Client(server_url) as client:
                print("✅ Connected to MCP server successfully!")
                
                # List available tools
                print("\n🔧 Listing available tools...")
                # Note: This would require the actual FastMCP client API
                # For now, we'll assume long_task is available
                
                print("📋 Available tools: long_task")
                
                # Call the long_task tool
                print("\n🚀 Calling long_task tool...")
                print("📊 Expected: 5 progress updates with visual progress bars")
                print("ℹ️  Expected: Multiple info messages during execution")
                print("-" * 50)
                
                start_time = time.time()
                
                # This would be the actual tool call
                result = await client.call_tool("long_task", args={})
                
                end_time = time.time()
                duration = end_time - start_time
                
                print("-" * 50)
                print(f"✅ Tool execution completed in {duration:.2f} seconds")
                print(f"📊 Result: {result}")
                
                return True
                
        except Exception as e:
            print(f"❌ Failed to connect to MCP server: {e}")
            print("\n💡 To test with real MCP server:")
            print("1. Start the server in another terminal:")
            print("   cd gaia/gaia_ceto/proto_mcp/long_task_test")
            print("   python minimal_mcp_server.py")
            print("2. Wait for 'Server running on http://127.0.0.1:9000'")
            print("3. Run this script again")
            
            # Simulate what the real tool would do
            print("\n🎭 Simulating real tool behavior...")
            await simulate_real_tool_behavior()
            
            return False
    
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        shutil.rmtree(context_dir, ignore_errors=True)


async def simulate_real_tool_behavior():
    """Simulate what the real long_task tool would do."""
    print("\n📊 Simulating real long_task tool execution:")
    print("🚀 Starting long task with 5 steps...")
    
    for i in range(5):
        await asyncio.sleep(0.5)  # Simulate work
        
        # Calculate progress
        progress = i + 1
        total = 5
        pct = (progress / total) * 100
        
        # Create visual progress bar
        bar_length = 28
        filled = int(bar_length * pct / 100)
        bar = "█" * filled + "░" * (bar_length - filled)
        
        # Display progress
        print(f"📊 [{bar}] {pct:5.1f}% {progress}/{total} – Step {progress} complete")
        print(f"ℹ️  [INFO] Completed step {progress}/5")
        
        if progress < total:
            print(f"ℹ️  [INFO] Processing next step...")
    
    print("ℹ️  [INFO] 🎉 Long task completed successfully!")
    print("\n✅ Simulation completed!")
    print("📊 Progress updates: 6")
    print("ℹ️  Info messages: 11")
    print("🎯 Result: {'status': 'completed', 'steps': 5, 'message': 'Long task finished'}")


def test_chat_term_integration():
    """Test chat_term integration with context and tool detection."""
    print("\n" + "="*70)
    print("TESTING CHAT_TERM INTEGRATION")
    print("="*70)
    
    # Create temporary directories
    temp_dir = tempfile.mkdtemp()
    context_dir = tempfile.mkdtemp()
    
    try:
        # Create context file
        context_data = {
            "type": "test_data",
            "title": "Integration Test Data",
            "description": "Test data for chat_term integration testing",
            "data": ["item1", "item2", "item3"]
        }
        
        context_file = os.path.join(context_dir, "integration_test.json")
        with open(context_file, 'w', encoding='utf-8') as f:
            json.dump(context_data, f, indent=2)
        
        # Create terminal
        terminal = ChatTerminal(
            storage_dir=temp_dir,
            llm_provider="mock"
        )
        terminal.context_dir = context_dir
        
        # Test tool detection
        print("🔍 Testing tool call detection...")
        
        test_inputs = [
            "long_task",
            "long_task()",
            "echostring_longrunning",
            "Hello, please run long_task",
            "Can you help me?"
        ]
        
        for test_input in test_inputs:
            is_direct, tool_name = terminal._is_direct_tool_call(test_input)
            status = "✅ DIRECT TOOL" if is_direct else "❌ NOT DIRECT"
            tool_info = f" -> {tool_name}" if tool_name else ""
            print(f"  '{test_input}' -> {status}{tool_info}")
        
        # Test context loading
        print("\n📁 Testing context loading...")
        terminal.cmd_load_context("integration_test.json")
        
        print("✅ Integration test completed successfully!")
        
        return True
    
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        shutil.rmtree(context_dir, ignore_errors=True)


async def main():
    """Main test function."""
    print("🧪 REAL LONG_TASK TOOL TESTING SUITE")
    print("=" * 70)
    
    # Test 1: Chat_term integration
    print("\n1️⃣  Testing chat_term integration...")
    integration_success = test_chat_term_integration()
    
    # Test 2: Real tool testing
    print("\n2️⃣  Testing real long_task tool...")
    real_tool_success = await test_real_long_task_tool()
    
    # Summary
    print("\n" + "="*70)
    print("TEST SUMMARY")
    print("="*70)
    print(f"✅ Chat_term integration: {'PASSED' if integration_success else 'FAILED'}")
    print(f"{'✅' if real_tool_success else '❌'} Real tool testing: {'PASSED' if real_tool_success else 'SIMULATED'}")
    
    if not real_tool_success:
        print("\n💡 To test with real MCP server:")
        print("1. Install FastMCP: pip install fastmcp")
        print("2. Start MCP server: cd gaia/gaia_ceto/proto_mcp/long_task_test && python minimal_mcp_server.py")
        print("3. Run this script again")
    
    print("\n🎉 Testing completed!")


if __name__ == "__main__":
    asyncio.run(main())
