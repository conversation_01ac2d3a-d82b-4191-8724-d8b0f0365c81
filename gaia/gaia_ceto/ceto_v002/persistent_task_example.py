#!/usr/bin/env python3
"""
Example implementation of persistent long-running tasks with database backing.

This demonstrates how to implement long-running tasks that survive client disconnections
and allow reconnection to check status and receive updates.
"""

import asyncio
import json
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

# Simulated MCP server framework
class Context:
    """Simulated MCP Context for demonstration."""
    def __init__(self, client_id: str = None):
        self.client_id = client_id
        self.connected = True
    
    async def report_progress(self, progress: int, total: int):
        if self.connected:
            print(f"📊 [{progress}/{total}] Progress: {(progress/total)*100:.1f}%")
    
    async def info(self, message: str):
        if self.connected:
            print(f"ℹ️  {message}")


class PersistentTaskManager:
    """Manages persistent long-running tasks with database backing."""
    
    def __init__(self, db_path: str = "tasks.db"):
        self.db_path = db_path
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.init_database()
    
    def init_database(self):
        """Initialize the SQLite database for task storage."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tasks (
                job_id TEXT PRIMARY KEY,
                status TEXT NOT NULL,
                progress INTEGER DEFAULT 0,
                total INTEGER DEFAULT 0,
                started_at TEXT NOT NULL,
                completed_at TEXT,
                current_step INTEGER DEFAULT 0,
                result TEXT,
                error TEXT,
                task_type TEXT NOT NULL,
                metadata TEXT
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS task_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                job_id TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                message TEXT NOT NULL,
                message_type TEXT DEFAULT 'info',
                FOREIGN KEY (job_id) REFERENCES tasks (job_id)
            )
        """)
        
        conn.commit()
        conn.close()
    
    def create_task(self, task_type: str, metadata: Dict[str, Any] = None) -> str:
        """Create a new persistent task."""
        job_id = str(uuid.uuid4())
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO tasks (job_id, status, started_at, task_type, metadata)
            VALUES (?, ?, ?, ?, ?)
        """, (
            job_id,
            "created",
            datetime.now().isoformat(),
            task_type,
            json.dumps(metadata or {})
        ))
        
        conn.commit()
        conn.close()
        
        return job_id
    
    def update_task_status(self, job_id: str, status: str, progress: int = None, 
                          current_step: int = None, result: Any = None, error: str = None):
        """Update task status in database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        updates = ["status = ?"]
        values = [status]
        
        if progress is not None:
            updates.append("progress = ?")
            values.append(progress)
        
        if current_step is not None:
            updates.append("current_step = ?")
            values.append(current_step)
        
        if result is not None:
            updates.append("result = ?")
            values.append(json.dumps(result))
        
        if error is not None:
            updates.append("error = ?")
            values.append(error)
        
        if status in ["completed", "failed"]:
            updates.append("completed_at = ?")
            values.append(datetime.now().isoformat())
        
        values.append(job_id)
        
        cursor.execute(f"""
            UPDATE tasks SET {', '.join(updates)}
            WHERE job_id = ?
        """, values)
        
        conn.commit()
        conn.close()
    
    def add_task_message(self, job_id: str, message: str, message_type: str = "info"):
        """Add a message to the task log."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO task_messages (job_id, timestamp, message, message_type)
            VALUES (?, ?, ?, ?)
        """, (job_id, datetime.now().isoformat(), message, message_type))
        
        conn.commit()
        conn.close()
    
    def get_task_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get current task status from database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT job_id, status, progress, total, started_at, completed_at,
                   current_step, result, error, task_type, metadata
            FROM tasks WHERE job_id = ?
        """, (job_id,))
        
        row = cursor.fetchone()
        if not row:
            conn.close()
            return None
        
        # Get recent messages
        cursor.execute("""
            SELECT timestamp, message, message_type
            FROM task_messages
            WHERE job_id = ?
            ORDER BY timestamp DESC
            LIMIT 10
        """, (job_id,))
        
        messages = cursor.fetchall()
        conn.close()
        
        return {
            "job_id": row[0],
            "status": row[1],
            "progress": row[2],
            "total": row[3],
            "started_at": row[4],
            "completed_at": row[5],
            "current_step": row[6],
            "result": json.loads(row[7]) if row[7] else None,
            "error": row[8],
            "task_type": row[9],
            "metadata": json.loads(row[10]) if row[10] else {},
            "recent_messages": [
                {"timestamp": msg[0], "message": msg[1], "type": msg[2]}
                for msg in reversed(messages)
            ]
        }
    
    def list_active_tasks(self) -> list:
        """List all active (non-completed) tasks."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT job_id, status, progress, total, started_at, task_type
            FROM tasks
            WHERE status NOT IN ('completed', 'failed')
            ORDER BY started_at DESC
        """)
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            {
                "job_id": row[0],
                "status": row[1],
                "progress": row[2],
                "total": row[3],
                "started_at": row[4],
                "task_type": row[5]
            }
            for row in rows
        ]
    
    async def start_long_task(self, job_id: str, ctx: Context = None):
        """Start the actual long-running task."""
        self.update_task_status(job_id, "running", progress=0, current_step=0)
        self.add_task_message(job_id, "Starting long task with 5 steps...")
        
        try:
            # Set total steps
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("UPDATE tasks SET total = ? WHERE job_id = ?", (5, job_id))
            conn.commit()
            conn.close()
            
            for i in range(5):
                # Simulate work
                await asyncio.sleep(2)
                
                # Update progress
                step = i + 1
                self.update_task_status(job_id, "running", progress=step, current_step=step)
                self.add_task_message(job_id, f"Completed step {step}/5")
                
                # Try to send progress to client if connected
                if ctx and ctx.connected:
                    try:
                        await ctx.report_progress(step, 5)
                        await ctx.info(f"Completed step {step}/5")
                    except:
                        # Client disconnected
                        ctx.connected = False
                        self.add_task_message(job_id, "Client disconnected, continuing in background")
            
            # Task completed successfully
            result = {"status": "completed", "steps": 5, "message": "Long task finished"}
            self.update_task_status(job_id, "completed", result=result)
            self.add_task_message(job_id, "🎉 Long task completed successfully!")
            
        except Exception as e:
            # Task failed
            self.update_task_status(job_id, "failed", error=str(e))
            self.add_task_message(job_id, f"❌ Task failed: {e}")
        
        finally:
            # Clean up from active tasks
            if job_id in self.active_tasks:
                del self.active_tasks[job_id]
    
    async def reconnect_to_task(self, job_id: str, ctx: Context) -> bool:
        """Reconnect to an existing task and receive updates."""
        task_info = self.get_task_status(job_id)
        if not task_info:
            await ctx.info(f"❌ Task {job_id} not found")
            return False
        
        # Send current status
        await ctx.info(f"📋 Reconnected to task: {task_info['status']}")
        await ctx.info(f"📊 Progress: {task_info['progress']}/{task_info['total']}")
        
        # Send recent messages
        for msg in task_info['recent_messages'][-5:]:
            await ctx.info(f"[{msg['timestamp'][:19]}] {msg['message']}")
        
        # If task is still running, send current progress
        if task_info['status'] == 'running':
            await ctx.report_progress(task_info['progress'], task_info['total'])
            await ctx.info("🔄 Task is still running...")
            return True
        elif task_info['status'] == 'completed':
            await ctx.info("✅ Task completed!")
            if task_info['result']:
                await ctx.info(f"Result: {task_info['result']}")
            return True
        elif task_info['status'] == 'failed':
            await ctx.info(f"❌ Task failed: {task_info['error']}")
            return True
        
        return False


# Example usage and MCP tool implementations
task_manager = PersistentTaskManager()

async def mcp_start_persistent_long_task(ctx: Context = None) -> str:
    """MCP tool to start a persistent long task."""
    job_id = task_manager.create_task("long_task", {"description": "5-step long task"})
    
    # Start the task in background
    task = asyncio.create_task(task_manager.start_long_task(job_id, ctx))
    task_manager.active_tasks[job_id] = task
    
    if ctx:
        await ctx.info(f"🚀 Started persistent long task")
        await ctx.info(f"📋 Job ID: {job_id}")
        await ctx.info("💡 You can disconnect and reconnect using the job ID")
    
    return job_id

async def mcp_reconnect_to_task(job_id: str, ctx: Context = None) -> str:
    """MCP tool to reconnect to an existing task."""
    if not job_id:
        return "❌ Please provide a job ID"
    
    success = await task_manager.reconnect_to_task(job_id, ctx)
    return "✅ Reconnected successfully" if success else "❌ Failed to reconnect"

async def mcp_list_active_tasks(ctx: Context = None) -> str:
    """MCP tool to list all active tasks."""
    tasks = task_manager.list_active_tasks()
    
    if not tasks:
        return "No active tasks found"
    
    result = "📋 Active Tasks:\n"
    for task in tasks:
        result += f"  • {task['job_id'][:8]}... - {task['status']} ({task['progress']}/{task['total']})\n"
        result += f"    Type: {task['task_type']}, Started: {task['started_at'][:19]}\n"
    
    return result


# Demo function
async def demo_persistent_tasks():
    """Demonstrate persistent task functionality."""
    print("🧪 Persistent Task Demo")
    print("=" * 50)
    
    # Create a context (simulating a client)
    ctx = Context("client_1")
    
    # Start a task
    print("\n1️⃣  Starting persistent long task...")
    job_id = await mcp_start_persistent_long_task(ctx)
    print(f"Job ID: {job_id}")
    
    # Simulate client disconnection after 3 seconds
    await asyncio.sleep(3)
    print("\n💔 Simulating client disconnection...")
    ctx.connected = False
    
    # Wait a bit more
    await asyncio.sleep(2)
    
    # Reconnect with new context
    print("\n🔄 Reconnecting to task...")
    new_ctx = Context("client_2")
    await mcp_reconnect_to_task(job_id, new_ctx)
    
    # Wait for task to complete
    await asyncio.sleep(5)
    
    # Check final status
    print("\n📊 Final status check...")
    await mcp_reconnect_to_task(job_id, new_ctx)


if __name__ == "__main__":
    asyncio.run(demo_persistent_tasks())
