#!/usr/bin/env python3
"""
Simple test for URL template resolution functionality.

This test focuses on the core URL template resolution feature
without requiring actual MCP client connections.
"""

import os
import sys

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Also add current directory for local imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from mcp_server_registry import MCPServerRegistry, MCPServerInfo


def test_url_template_resolution():
    """Test URL template resolution with API key substitution."""
    print("🧪 Testing URL Template Resolution")
    print("=" * 50)
    
    # Test 1: Create a test server with URL template
    print("\n1️⃣  Testing URL template creation...")
    test_server = MCPServerInfo(
        name="Test Firecrawl Server",
        description="Test server for URL resolution",
        protocol="sse",
        url="https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
        type="third_party",
        api_key_template="https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse"
    )
    print(f"✅ Created test server: {test_server.name}")
    print(f"   URL template: {test_server.url}")
    
    # Test 2: Resolution without API key (should fail)
    print("\n2️⃣  Testing resolution without API key...")
    # Remove API key if it exists
    original_key = os.environ.pop('FIRECRAWL_API_KEY', None)
    
    resolved_url = test_server.get_resolved_url()
    if resolved_url is None:
        print("✅ Correctly failed when API key is missing")
    else:
        print(f"❌ Unexpectedly resolved URL: {resolved_url}")
    
    # Test 3: Resolution with API key (should succeed)
    print("\n3️⃣  Testing resolution with API key...")
    test_api_key = "fc-test-12345-abcdef"
    os.environ['FIRECRAWL_API_KEY'] = test_api_key
    
    resolved_url = test_server.get_resolved_url()
    expected_url = f"https://mcp.firecrawl.dev/{test_api_key}/sse"
    
    if resolved_url == expected_url:
        print(f"✅ Successfully resolved URL: {resolved_url}")
    else:
        print(f"❌ URL resolution failed")
        print(f"   Expected: {expected_url}")
        print(f"   Got: {resolved_url}")
    
    # Test 4: Multiple placeholders
    print("\n4️⃣  Testing multiple environment variables...")
    multi_server = MCPServerInfo(
        name="Multi-Var Server",
        description="Server with multiple environment variables",
        protocol="http",
        url="https://api.example.com/{API_KEY}/v1/{USER_ID}/endpoint",
        type="third_party"
    )
    
    # Set test environment variables
    os.environ['API_KEY'] = "key123"
    os.environ['USER_ID'] = "user456"
    
    resolved_multi = multi_server.get_resolved_url()
    expected_multi = "https://api.example.com/key123/v1/user456/endpoint"
    
    if resolved_multi == expected_multi:
        print(f"✅ Multi-variable resolution successful: {resolved_multi}")
    else:
        print(f"❌ Multi-variable resolution failed")
        print(f"   Expected: {expected_multi}")
        print(f"   Got: {resolved_multi}")
    
    # Cleanup
    if original_key:
        os.environ['FIRECRAWL_API_KEY'] = original_key
    else:
        os.environ.pop('FIRECRAWL_API_KEY', None)
    
    os.environ.pop('API_KEY', None)
    os.environ.pop('USER_ID', None)
    
    return True


def test_registry_loading():
    """Test loading the MCP server registry."""
    print("\n🧪 Testing MCP Server Registry")
    print("=" * 50)
    
    try:
        # Load the registry
        print("\n1️⃣  Loading server registry...")
        registry = MCPServerRegistry()
        servers = registry.list_servers()
        print(f"✅ Loaded {len(servers)} servers from registry")
        
        # Find the Firecrawl hosted server
        print("\n2️⃣  Looking for Firecrawl hosted server...")
        firecrawl_server = registry.get_server("example_firecrawl_hosted")
        
        if firecrawl_server:
            print(f"✅ Found Firecrawl server: {firecrawl_server.name}")
            print(f"   Protocol: {firecrawl_server.protocol}")
            print(f"   URL template: {firecrawl_server.url}")
            print(f"   Tools: {len(firecrawl_server.tools or [])} tools")
            
            # Test URL resolution with the registry server
            print("\n3️⃣  Testing URL resolution with registry server...")
            os.environ['FIRECRAWL_API_KEY'] = "fc-test-registry-key"
            
            resolved = firecrawl_server.get_resolved_url()
            if resolved:
                print(f"✅ Registry server URL resolved: {resolved[:60]}...")
            else:
                print("❌ Failed to resolve registry server URL")
            
            # Clean up
            os.environ.pop('FIRECRAWL_API_KEY', None)
            
        else:
            print("❌ Firecrawl hosted server not found in registry")
            print("   Available servers:")
            for server_id, server_info in registry.servers.items():
                print(f"     - {server_id}: {server_info.name}")
        
        # Test third-party server filtering
        print("\n4️⃣  Testing third-party server filtering...")
        third_party_servers = registry.list_servers(server_type="third_party")
        print(f"✅ Found {len(third_party_servers)} third-party servers")
        
        for server in third_party_servers[:3]:  # Show first 3
            print(f"   - {server.name} ({server.protocol})")
        
        return True
        
    except Exception as e:
        print(f"❌ Registry test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 URL Template Resolution Test Suite")
    print("=" * 60)
    
    success = True
    
    # Test URL template resolution
    try:
        success &= test_url_template_resolution()
    except Exception as e:
        print(f"❌ URL template test failed: {e}")
        success = False
    
    # Test registry loading
    try:
        success &= test_registry_loading()
    except Exception as e:
        print(f"❌ Registry test failed: {e}")
        success = False
    
    # Summary
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests passed!")
        print("\n🎯 Key Features Verified:")
        print("   ✅ URL template resolution with environment variables")
        print("   ✅ Proper handling of missing environment variables")
        print("   ✅ Multiple environment variable substitution")
        print("   ✅ MCP server registry loading")
        print("   ✅ Third-party server configuration")
        
        print("\n🚀 Ready for Integration:")
        print("   • URL template resolution is working correctly")
        print("   • Firecrawl hosted server is properly configured")
        print("   • Registry can load and filter third-party servers")
        print("   • Environment variable validation is working")
        
    else:
        print("❌ Some tests failed!")
        return 1
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
