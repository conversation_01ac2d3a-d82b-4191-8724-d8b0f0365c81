#!/bin/bash
# Simple wrapper script to call long_task from command line

# Set the working directory
cd "$(dirname "$0")"

# Set PYTHONPATH to include the project root
export PYTHONPATH="/home/<USER>/django-projects/agbase_admin:$PYTHONPATH"

# Default values
SERVER="http://0.0.0.0:9000/mcp"
LLM="mcp-http"
TITLE="CLI Long Task $(date '+%Y-%m-%d %H:%M:%S')"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --server)
            SERVER="$2"
            shift 2
            ;;
        --llm)
            LLM="$2"
            shift 2
            ;;
        --context)
            CONTEXT="$2"
            shift 2
            ;;
        --title)
            TITLE="$2"
            shift 2
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --server URL     MCP server URL (default: http://0.0.0.0:9000/mcp)"
            echo "  --llm PROVIDER   LLM provider: mcp-http, mcp, mock (default: mcp-http)"
            echo "  --context FILE   Context JSON file to load"
            echo "  --title TITLE    Conversation title"
            echo "  --help, -h       Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Basic usage"
            echo "  $0 --llm mock                         # Use MockLLM"
            echo "  $0 --context companies.json           # Load context"
            echo "  $0 --server http://localhost:9000/mcp # Custom server"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Build the command
CMD="python call_long_task.py --server '$SERVER' --llm '$LLM' --title '$TITLE' --verbose"

if [[ -n "$CONTEXT" ]]; then
    CMD="$CMD --context '$CONTEXT'"
fi

echo "🚀 Calling long_task with:"
echo "   Server: $SERVER"
echo "   LLM: $LLM"
echo "   Title: $TITLE"
if [[ -n "$CONTEXT" ]]; then
    echo "   Context: $CONTEXT"
fi
echo ""

# Execute the command
eval $CMD
