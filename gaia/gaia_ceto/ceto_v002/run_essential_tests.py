#!/usr/bin/env python3
"""
Test runner for essential chat_term functionality tests.

This script runs the essential tests for chat_term.py functionality:
- Progressive streaming response using the long_task tool
- Loading of context data using the load_context command

Usage:
    python run_essential_tests.py
    
Or run specific test classes:
    python run_essential_tests.py TestChatTerminalEssentials
    python run_essential_tests.py TestAsyncFunctionality
    
Or run specific test methods:
    python run_essential_tests.py TestChatTerminalEssentials.test_load_context_functionality
"""

import sys
import unittest
import os

# Add the current directory to the Python path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the test modules
from tests_essential import TestChatTerminalEssentials, TestAsyncFunctionality, TestRealLongTaskTool


def run_all_tests():
    """Run all essential tests."""
    print("=" * 70)
    print("RUNNING ESSENTIAL CHAT_TERM TESTS")
    print("=" * 70)
    print()
    
    # Create a test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add all test classes
    suite.addTests(loader.loadTestsFromTestCase(TestChatTerminalEssentials))
    suite.addTests(loader.loadTestsFromTestCase(TestAsyncFunctionality))
    suite.addTests(loader.loadTestsFromTestCase(TestRealLongTaskTool))
    
    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print()
    print("=" * 70)
    print("TEST SUMMARY")
    print("=" * 70)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # Return success/failure
    return len(result.failures) == 0 and len(result.errors) == 0


def run_specific_test(test_name):
    """Run a specific test class or method."""
    print(f"Running specific test: {test_name}")
    print("=" * 70)
    
    # Create a test suite for the specific test
    suite = unittest.TestSuite()
    
    if '.' in test_name:
        # It's a specific test method
        class_name, method_name = test_name.split('.', 1)
        if class_name == 'TestChatTerminalEssentials':
            suite.addTest(TestChatTerminalEssentials(method_name))
        elif class_name == 'TestAsyncFunctionality':
            suite.addTest(TestAsyncFunctionality(method_name))
        elif class_name == 'TestRealLongTaskTool':
            suite.addTest(TestRealLongTaskTool(method_name))
        else:
            print(f"Unknown test class: {class_name}")
            return False
    else:
        # It's a test class
        loader = unittest.TestLoader()
        if test_name == 'TestChatTerminalEssentials':
            suite.addTests(loader.loadTestsFromTestCase(TestChatTerminalEssentials))
        elif test_name == 'TestAsyncFunctionality':
            suite.addTests(loader.loadTestsFromTestCase(TestAsyncFunctionality))
        elif test_name == 'TestRealLongTaskTool':
            suite.addTests(loader.loadTestsFromTestCase(TestRealLongTaskTool))
        else:
            print(f"Unknown test class: {test_name}")
            return False
    
    # Run the test
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return len(result.failures) == 0 and len(result.errors) == 0


def main():
    """Main entry point."""
    if len(sys.argv) == 1:
        # Run all tests
        success = run_all_tests()
    elif len(sys.argv) == 2:
        # Run specific test
        test_name = sys.argv[1]
        success = run_specific_test(test_name)
    else:
        print("Usage:")
        print("  python run_essential_tests.py                    # Run all tests")
        print("  python run_essential_tests.py TestClassName      # Run specific test class")
        print("  python run_essential_tests.py TestClass.method   # Run specific test method")
        print()
        print("Available test classes:")
        print("  - TestChatTerminalEssentials")
        print("  - TestAsyncFunctionality")
        print("  - TestRealLongTaskTool")
        return 1
    
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
