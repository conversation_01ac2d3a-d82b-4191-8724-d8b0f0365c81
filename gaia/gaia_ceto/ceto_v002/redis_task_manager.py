#!/usr/bin/env python3
"""
Redis-based persistent task manager for production use.

This implementation uses Redis for:
- Task state storage
- Real-time progress updates via pub/sub
- Task queue management
- Client session tracking
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Set
import logging

# Redis imports (install with: pip install redis aioredis)
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("Redis not available. Install with: pip install redis aioredis")

logger = logging.getLogger(__name__)


class RedisTaskManager:
    """Redis-based persistent task manager."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis is required for persistent task management")
        
        self.redis_url = redis_url
        self.redis_client = None
        self.pubsub = None
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.connected_clients: Set[str] = set()
    
    async def connect(self):
        """Connect to Redis."""
        self.redis_client = redis.from_url(self.redis_url)
        self.pubsub = self.redis_client.pubsub()
        
        # Test connection
        await self.redis_client.ping()
        logger.info("Connected to Redis")
    
    async def disconnect(self):
        """Disconnect from Redis."""
        if self.pubsub:
            await self.pubsub.close()
        if self.redis_client:
            await self.redis_client.close()
    
    async def create_task(self, task_type: str, client_id: str = None, 
                         metadata: Dict[str, Any] = None) -> str:
        """Create a new persistent task."""
        job_id = str(uuid.uuid4())
        
        task_data = {
            "job_id": job_id,
            "task_type": task_type,
            "status": "created",
            "progress": 0,
            "total": 0,
            "started_at": datetime.now().isoformat(),
            "client_id": client_id,
            "metadata": metadata or {}
        }
        
        # Store task data
        await self.redis_client.hset(f"task:{job_id}", mapping={
            k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
            for k, v in task_data.items()
        })
        
        # Add to active tasks list
        await self.redis_client.sadd("active_tasks", job_id)
        
        # Set expiration (24 hours)
        await self.redis_client.expire(f"task:{job_id}", 86400)
        
        return job_id
    
    async def update_task(self, job_id: str, **updates):
        """Update task data."""
        if updates:
            await self.redis_client.hset(f"task:{job_id}", mapping={
                k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
                for k, v in updates.items()
            })
    
    async def get_task(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get task data."""
        data = await self.redis_client.hgetall(f"task:{job_id}")
        if not data:
            return None
        
        # Convert bytes to strings and parse JSON
        result = {}
        for k, v in data.items():
            key = k.decode() if isinstance(k, bytes) else k
            value = v.decode() if isinstance(v, bytes) else v
            
            # Try to parse as JSON
            try:
                result[key] = json.loads(value)
            except (json.JSONDecodeError, TypeError):
                result[key] = value
        
        return result
    
    async def add_task_message(self, job_id: str, message: str, message_type: str = "info"):
        """Add a message to task log."""
        message_data = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "type": message_type
        }
        
        # Add to task messages list
        await self.redis_client.lpush(f"task:{job_id}:messages", json.dumps(message_data))
        
        # Keep only last 100 messages
        await self.redis_client.ltrim(f"task:{job_id}:messages", 0, 99)
        
        # Publish real-time update
        await self.redis_client.publish(f"task_updates:{job_id}", json.dumps({
            "type": "message",
            "data": message_data
        }))
    
    async def publish_progress(self, job_id: str, progress: int, total: int, message: str = None):
        """Publish progress update."""
        progress_data = {
            "progress": progress,
            "total": total,
            "timestamp": datetime.now().isoformat()
        }
        
        if message:
            progress_data["message"] = message
        
        # Update task data
        await self.update_task(job_id, progress=progress, total=total)
        
        # Publish real-time update
        await self.redis_client.publish(f"task_updates:{job_id}", json.dumps({
            "type": "progress",
            "data": progress_data
        }))
    
    async def subscribe_to_task(self, job_id: str, callback):
        """Subscribe to real-time task updates."""
        await self.pubsub.subscribe(f"task_updates:{job_id}")
        
        async for message in self.pubsub.listen():
            if message["type"] == "message":
                try:
                    data = json.loads(message["data"])
                    await callback(data)
                except Exception as e:
                    logger.error(f"Error processing task update: {e}")
    
    async def list_active_tasks(self) -> list:
        """List all active tasks."""
        task_ids = await self.redis_client.smembers("active_tasks")
        tasks = []
        
        for task_id in task_ids:
            if isinstance(task_id, bytes):
                task_id = task_id.decode()
            
            task_data = await self.get_task(task_id)
            if task_data:
                tasks.append(task_data)
        
        return tasks
    
    async def start_long_task(self, job_id: str, steps: int = 5):
        """Start a long-running task."""
        await self.update_task(job_id, status="running", total=steps)
        await self.add_task_message(job_id, f"Starting long task with {steps} steps...")
        
        try:
            for i in range(steps):
                # Simulate work
                await asyncio.sleep(2)
                
                step = i + 1
                await self.publish_progress(job_id, step, steps, f"Completed step {step}/{steps}")
                await self.add_task_message(job_id, f"Step {step} completed")
            
            # Task completed
            result = {"status": "completed", "steps": steps}
            await self.update_task(job_id, 
                                 status="completed", 
                                 completed_at=datetime.now().isoformat(),
                                 result=result)
            await self.add_task_message(job_id, "🎉 Task completed successfully!")
            
            # Remove from active tasks
            await self.redis_client.srem("active_tasks", job_id)
            
        except Exception as e:
            await self.update_task(job_id, 
                                 status="failed", 
                                 error=str(e),
                                 completed_at=datetime.now().isoformat())
            await self.add_task_message(job_id, f"❌ Task failed: {e}")
            await self.redis_client.srem("active_tasks", job_id)
        
        finally:
            if job_id in self.active_tasks:
                del self.active_tasks[job_id]


class TaskClient:
    """Client for interacting with persistent tasks."""
    
    def __init__(self, task_manager: RedisTaskManager, client_id: str = None):
        self.task_manager = task_manager
        self.client_id = client_id or str(uuid.uuid4())
        self.subscriptions: Dict[str, asyncio.Task] = {}
    
    async def start_task(self, task_type: str = "long_task", metadata: Dict = None) -> str:
        """Start a new persistent task."""
        job_id = await self.task_manager.create_task(
            task_type=task_type,
            client_id=self.client_id,
            metadata=metadata
        )
        
        # Start the task
        task = asyncio.create_task(self.task_manager.start_long_task(job_id))
        self.task_manager.active_tasks[job_id] = task
        
        return job_id
    
    async def reconnect_to_task(self, job_id: str) -> bool:
        """Reconnect to an existing task."""
        task_data = await self.task_manager.get_task(job_id)
        if not task_data:
            return False
        
        print(f"📋 Reconnected to task: {task_data['status']}")
        print(f"📊 Progress: {task_data['progress']}/{task_data['total']}")
        
        # Get recent messages
        messages = await self.task_manager.redis_client.lrange(f"task:{job_id}:messages", 0, 4)
        for msg_data in reversed(messages):
            if isinstance(msg_data, bytes):
                msg_data = msg_data.decode()
            msg = json.loads(msg_data)
            print(f"[{msg['timestamp'][:19]}] {msg['message']}")
        
        return True
    
    async def subscribe_to_task_updates(self, job_id: str):
        """Subscribe to real-time updates for a task."""
        async def update_callback(data):
            if data["type"] == "progress":
                progress_data = data["data"]
                progress = progress_data["progress"]
                total = progress_data["total"]
                pct = (progress / total) * 100 if total > 0 else 0
                
                # Create progress bar
                bar_length = 20
                filled = int(bar_length * pct / 100)
                bar = "█" * filled + "░" * (bar_length - filled)
                
                print(f"📊 [{bar}] {pct:5.1f}% {progress}/{total}")
                
                if "message" in progress_data:
                    print(f"ℹ️  {progress_data['message']}")
            
            elif data["type"] == "message":
                msg_data = data["data"]
                print(f"ℹ️  {msg_data['message']}")
        
        # Start subscription task
        sub_task = asyncio.create_task(
            self.task_manager.subscribe_to_task(job_id, update_callback)
        )
        self.subscriptions[job_id] = sub_task
        
        return sub_task
    
    async def list_my_tasks(self) -> list:
        """List tasks created by this client."""
        all_tasks = await self.task_manager.list_active_tasks()
        return [task for task in all_tasks if task.get("client_id") == self.client_id]


# Example usage
async def demo_redis_persistent_tasks():
    """Demonstrate Redis-based persistent tasks."""
    if not REDIS_AVAILABLE:
        print("❌ Redis not available. Install with: pip install redis aioredis")
        return
    
    print("🧪 Redis Persistent Task Demo")
    print("=" * 50)
    
    # Create task manager
    task_manager = RedisTaskManager()
    await task_manager.connect()
    
    try:
        # Create client
        client = TaskClient(task_manager, "demo_client")
        
        # Start a task
        print("\n1️⃣  Starting persistent task...")
        job_id = await client.start_task("long_task", {"description": "Demo task"})
        print(f"Job ID: {job_id}")
        
        # Subscribe to updates
        print("\n2️⃣  Subscribing to real-time updates...")
        sub_task = await client.subscribe_to_task_updates(job_id)
        
        # Simulate disconnection after 3 seconds
        await asyncio.sleep(3)
        print("\n💔 Simulating client disconnection...")
        sub_task.cancel()
        
        # Wait a bit
        await asyncio.sleep(2)
        
        # Reconnect
        print("\n🔄 Reconnecting...")
        await client.reconnect_to_task(job_id)
        
        # Subscribe again
        await client.subscribe_to_task_updates(job_id)
        
        # Wait for completion
        await asyncio.sleep(8)
        
    finally:
        await task_manager.disconnect()


if __name__ == "__main__":
    asyncio.run(demo_redis_persistent_tasks())
