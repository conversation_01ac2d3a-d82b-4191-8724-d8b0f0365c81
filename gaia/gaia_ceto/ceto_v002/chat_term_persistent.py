#!/usr/bin/env python3
"""
Enhanced chat_term with persistent task support.

This version adds commands for managing persistent long-running tasks that survive
client disconnections and allow reconnection.
"""

import asyncio
import os
import sys
from typing import Optional

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from chat_term import ChatTerminal
from persistent_task_example import PersistentTaskManager


class PersistentChatTerminal(ChatTerminal):
    """Enhanced ChatTerminal with persistent task support."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Initialize persistent task manager
        self.task_manager = PersistentTaskManager()
        
        # Add new commands
        self.commands.update({
            "start_persistent": self.cmd_start_persistent_task,
            "reconnect": self.cmd_reconnect_to_task,
            "list_tasks": self.cmd_list_tasks,
            "task_status": self.cmd_task_status,
        })
        
        # Add command help
        self.command_help.update({
            "start_persistent": "Start a persistent long task that survives disconnection",
            "reconnect": "Reconnect to a persistent task by job ID",
            "list_tasks": "List all active persistent tasks",
            "task_status": "Check status of a specific task",
        })
    
    def cmd_start_persistent_task(self, *args):
        """Start a persistent long task."""
        task_type = args[0] if args else "long_task"
        
        print(f"🚀 Starting persistent {task_type}...")
        
        # Create the task
        job_id = self.task_manager.create_task(task_type, {
            "description": f"Persistent {task_type} started from chat_term",
            "user_id": self.user_id
        })
        
        print(f"📋 Job ID: {job_id}")
        print("💡 You can disconnect and reconnect using: /reconnect {job_id}")
        print("💡 Or check status with: /task_status {job_id}")
        
        # Start the task in background
        async def start_task():
            # Create a simple context for progress reporting
            class SimpleContext:
                def __init__(self):
                    self.connected = True
                
                async def report_progress(self, progress, total):
                    if self.connected:
                        pct = (progress / total) * 100
                        bar_length = 20
                        filled = int(bar_length * pct / 100)
                        bar = "█" * filled + "░" * (bar_length - filled)
                        print(f"📊 [{bar}] {pct:5.1f}% {progress}/{total}")
                
                async def info(self, message):
                    if self.connected:
                        print(f"ℹ️  {message}")
            
            ctx = SimpleContext()
            task = asyncio.create_task(self.task_manager.start_long_task(job_id, ctx))
            self.task_manager.active_tasks[job_id] = task
            
            try:
                await task
            except Exception as e:
                print(f"❌ Task failed: {e}")
        
        # Run the async task
        asyncio.create_task(start_task())
        
        # Add to conversation if active
        if self.chat_manager.active_conversation:
            self.chat_manager.add_message("user", f"/start_persistent {task_type}")
            self.chat_manager.add_message("assistant", f"Started persistent task with ID: {job_id}")
            self.chat_manager.save_conversation()
    
    def cmd_reconnect_to_task(self, *args):
        """Reconnect to a persistent task."""
        if not args:
            print("Please provide a job ID.")
            print("Usage: /reconnect <job_id>")
            return
        
        job_id = args[0]
        print(f"🔄 Reconnecting to task: {job_id}")
        
        # Create async context for reconnection
        async def reconnect():
            class SimpleContext:
                def __init__(self):
                    self.connected = True
                
                async def report_progress(self, progress, total):
                    pct = (progress / total) * 100
                    bar_length = 20
                    filled = int(bar_length * pct / 100)
                    bar = "█" * filled + "░" * (bar_length - filled)
                    print(f"📊 [{bar}] {pct:5.1f}% {progress}/{total}")
                
                async def info(self, message):
                    print(f"ℹ️  {message}")
            
            ctx = SimpleContext()
            success = await self.task_manager.reconnect_to_task(job_id, ctx)
            
            if not success:
                print(f"❌ Could not reconnect to task: {job_id}")
            else:
                print("✅ Reconnected successfully!")
                
                # If task is still running, continue showing updates
                task_info = self.task_manager.get_task_status(job_id)
                if task_info and task_info['status'] == 'running':
                    print("🔄 Task is still running. Monitoring for updates...")
                    # Note: In a full implementation, you'd set up real-time monitoring here
        
        # Run the async reconnection
        asyncio.create_task(reconnect())
    
    def cmd_list_tasks(self, *args):
        """List all active persistent tasks."""
        tasks = self.task_manager.list_active_tasks()
        
        if not tasks:
            print("No active persistent tasks found.")
            return
        
        print(f"\n📋 Active Persistent Tasks ({len(tasks)}):")
        print("-" * 60)
        
        for task in tasks:
            job_id = task['job_id']
            status = task['status']
            progress = task['progress']
            total = task['total']
            task_type = task['task_type']
            started = task['started_at'][:19]  # Remove microseconds
            
            print(f"🔧 {task_type}")
            print(f"   ID: {job_id}")
            print(f"   Status: {status}")
            if total > 0:
                pct = (progress / total) * 100
                print(f"   Progress: {progress}/{total} ({pct:.1f}%)")
            print(f"   Started: {started}")
            print()
    
    def cmd_task_status(self, *args):
        """Check status of a specific task."""
        if not args:
            print("Please provide a job ID.")
            print("Usage: /task_status <job_id>")
            return
        
        job_id = args[0]
        task_info = self.task_manager.get_task_status(job_id)
        
        if not task_info:
            print(f"❌ Task not found: {job_id}")
            return
        
        print(f"\n📊 Task Status: {job_id}")
        print("-" * 50)
        print(f"Type: {task_info['task_type']}")
        print(f"Status: {task_info['status']}")
        print(f"Progress: {task_info['progress']}/{task_info['total']}")
        print(f"Started: {task_info['started_at'][:19]}")
        
        if task_info['completed_at']:
            print(f"Completed: {task_info['completed_at'][:19]}")
        
        if task_info['error']:
            print(f"Error: {task_info['error']}")
        
        if task_info['result']:
            print(f"Result: {task_info['result']}")
        
        # Show recent messages
        if task_info['recent_messages']:
            print("\nRecent Messages:")
            for msg in task_info['recent_messages'][-5:]:
                timestamp = msg['timestamp'][:19]
                message = msg['message']
                print(f"  [{timestamp}] {message}")
    
    def print_welcome(self):
        """Print enhanced welcome message."""
        super().print_welcome()
        
        print("\n📋 Persistent Task Features:")
        print("  /start_persistent    - Start a task that survives disconnection")
        print("  /reconnect <job_id>  - Reconnect to a running task")
        print("  /list_tasks          - List all active tasks")
        print("  /task_status <id>    - Check task status")
        print("=" * 60)


def main():
    """Main entry point for persistent chat terminal."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Chat Terminal with Persistent Task Support")
    parser.add_argument("--storage-dir", help="Directory to store conversations")
    parser.add_argument("--user-id", default="persistent_user", help="User ID")
    parser.add_argument("--llm", choices=["mock", "mcp-http", "mcp"], default="mock", help="LLM provider")
    parser.add_argument("--model", help="Model name or server URL")
    
    args = parser.parse_args()
    
    # Create the enhanced terminal
    terminal = PersistentChatTerminal(
        storage_dir=args.storage_dir,
        user_id=args.user_id,
        llm_provider=args.llm,
        model_name=args.model
    )
    
    try:
        terminal.start()
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
