#!/usr/bin/env python3
"""
Unified MCP Provider

This module provides a unified interface for connecting to and managing multiple MCP servers,
supporting both SSE and HTTP protocols, as well as third-party servers.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

try:
    from .mcp_server_registry import MCPServerRegistry, MCPServerInfo
except ImportError:
    from mcp_server_registry import MCPServerRegistry, MCPServerInfo

logger = logging.getLogger(__name__)

# Import MCP client libraries if available
MCP_SSE_AVAILABLE = False
MCP_HTTP_AVAILABLE = False

try:
    from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MC<PERSON>lient<PERSON>ib as MCPSSEClientLib
    MCP_SSE_AVAILABLE = True
except ImportError:
    logger.warning("MCP SSE client library not available")

try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MC<PERSON><PERSON><PERSON><PERSON> as MCPHTTPClientLib
    MCP_HTTP_AVAILABLE = True
except ImportError:
    logger.warning("MCP HTTP client library not available")


class MCPConnection:
    """Represents a connection to an MCP server."""
    
    def __init__(self, server_info: MCPServerInfo, client: Any):
        self.server_info = server_info
        self.client = client
        self.connected = False
        self.last_used = datetime.now()
        self.available_tools = []

    async def connect(self) -> bool:
        """Connect to the MCP server."""
        try:
            if self.server_info.protocol in ["sse", "http"]:
                # Get resolved URL (handles API key templates)
                resolved_url = self.server_info.get_resolved_url()
                if not resolved_url:
                    logger.error(f"Could not resolve URL for {self.server_info.name}. Check environment variables.")
                    return False

                logger.info(f"Connecting to {self.server_info.name} at {resolved_url}")
                success = await self.client.connect_to_server(resolved_url)
                if success:
                    self.connected = True
                    self.available_tools = getattr(self.client, 'available_tools', [])
                    logger.info(f"Connected to {self.server_info.name}")
                    return True
            else:
                logger.error(f"Unsupported protocol or missing URL: {self.server_info.protocol}")
                return False
        except Exception as e:
            logger.error(f"Failed to connect to {self.server_info.name}: {e}")
            return False

        return False

    async def disconnect(self):
        """Disconnect from the MCP server."""
        try:
            if hasattr(self.client, 'cleanup'):
                await self.client.cleanup()
            self.connected = False
            logger.info(f"Disconnected from {self.server_info.name}")
        except Exception as e:
            logger.error(f"Error disconnecting from {self.server_info.name}: {e}")

    async def call_tool(self, tool_name: str, tool_input: Any, **kwargs) -> Any:
        """Call a tool on this server."""
        if not self.connected:
            raise ConnectionError(f"Not connected to {self.server_info.name}")
        
        self.last_used = datetime.now()
        return await self.client.call_tool(tool_name, tool_input, **kwargs)

    async def process_query(self, query: str, **kwargs) -> Dict[str, Any]:
        """Process a query using this server."""
        if not self.connected:
            raise ConnectionError(f"Not connected to {self.server_info.name}")
        
        self.last_used = datetime.now()
        return await self.client.process_query(query, **kwargs)


class UnifiedMCPProvider:
    """Unified provider for managing multiple MCP servers."""
    
    def __init__(self, registry: Optional[MCPServerRegistry] = None):
        """Initialize the unified MCP provider.
        
        Args:
            registry: Server registry. If None, creates a default one.
        """
        self.registry = registry or MCPServerRegistry()
        self.connections: Dict[str, MCPConnection] = {}
        self.active_server: Optional[str] = None
        self.loop = asyncio.new_event_loop()

    def _create_client(self, server_info: MCPServerInfo) -> Optional[Any]:
        """Create an appropriate client for the server."""
        if server_info.protocol == "sse":
            if not MCP_SSE_AVAILABLE:
                logger.error("MCP SSE client not available")
                return None
            return MCPSSEClientLib(debug_callback=self._debug_callback)
        
        elif server_info.protocol == "http":
            if not MCP_HTTP_AVAILABLE:
                logger.error("MCP HTTP client not available")
                return None
            return MCPHTTPClientLib(debug_callback=self._debug_callback)
        
        elif server_info.protocol == "stdio":
            # TODO: Implement stdio client for third-party servers
            logger.error("STDIO protocol not yet implemented")
            return None
        
        else:
            logger.error(f"Unsupported protocol: {server_info.protocol}")
            return None

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Debug callback for MCP clients."""
        if level == "error":
            logger.error(message)
        elif level == "warning":
            logger.warning(message)
        elif level == "info":
            logger.info(message)
        else:
            logger.debug(message)

    async def connect_to_server(self, server_id: str) -> bool:
        """Connect to a specific server.
        
        Args:
            server_id: The server identifier.
        
        Returns:
            True if connection was successful.
        """
        server_info = self.registry.get_server(server_id)
        if not server_info:
            logger.error(f"Server not found: {server_id}")
            return False

        # Check if already connected
        if server_id in self.connections and self.connections[server_id].connected:
            logger.info(f"Already connected to {server_id}")
            return True

        # Create client
        client = self._create_client(server_info)
        if not client:
            return False

        # Create connection
        connection = MCPConnection(server_info, client)
        success = await connection.connect()
        
        if success:
            self.connections[server_id] = connection
            if not self.active_server:
                self.active_server = server_id
            return True
        
        return False

    async def disconnect_from_server(self, server_id: str):
        """Disconnect from a specific server."""
        if server_id in self.connections:
            await self.connections[server_id].disconnect()
            del self.connections[server_id]
            
            if self.active_server == server_id:
                # Switch to another connected server or None
                self.active_server = next(iter(self.connections.keys()), None)

    async def switch_server(self, server_id: str) -> bool:
        """Switch to a different server.
        
        Args:
            server_id: The server identifier.
        
        Returns:
            True if switch was successful.
        """
        if server_id not in self.connections:
            # Try to connect first
            success = await self.connect_to_server(server_id)
            if not success:
                return False
        
        self.active_server = server_id
        logger.info(f"Switched to server: {server_id}")
        return True

    def get_active_connection(self) -> Optional[MCPConnection]:
        """Get the active server connection."""
        if self.active_server and self.active_server in self.connections:
            return self.connections[self.active_server]
        return None

    def list_connected_servers(self) -> List[str]:
        """List currently connected servers."""
        return [server_id for server_id, conn in self.connections.items() if conn.connected]

    def get_available_tools(self, server_id: Optional[str] = None) -> List[str]:
        """Get available tools from a server.
        
        Args:
            server_id: Server to query. If None, uses active server.
        
        Returns:
            List of tool names.
        """
        target_server = server_id or self.active_server
        if target_server and target_server in self.connections:
            connection = self.connections[target_server]
            if connection.available_tools:
                return [tool['name'] if isinstance(tool, dict) else tool 
                       for tool in connection.available_tools]
        return []

    async def call_tool(self, tool_name: str, tool_input: Any, 
                       server_id: Optional[str] = None, **kwargs) -> Any:
        """Call a tool on a server.
        
        Args:
            tool_name: Name of the tool to call.
            tool_input: Input for the tool.
            server_id: Server to use. If None, uses active server.
            **kwargs: Additional arguments for the tool call.
        
        Returns:
            Tool call result.
        """
        target_server = server_id or self.active_server
        if not target_server or target_server not in self.connections:
            raise ConnectionError("No active server connection")
        
        connection = self.connections[target_server]
        return await connection.call_tool(tool_name, tool_input, **kwargs)

    async def process_query(self, query: str, server_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Process a query using a server.
        
        Args:
            query: The query to process.
            server_id: Server to use. If None, uses active server.
            **kwargs: Additional arguments for query processing.
        
        Returns:
            Query result.
        """
        target_server = server_id or self.active_server
        if not target_server or target_server not in self.connections:
            raise ConnectionError("No active server connection")
        
        connection = self.connections[target_server]
        return await connection.process_query(query, **kwargs)

    async def cleanup(self):
        """Clean up all connections."""
        for connection in self.connections.values():
            await connection.disconnect()
        self.connections.clear()
        self.active_server = None

    def generate_response(self, message: str) -> str:
        """Generate a response to a message (synchronous interface for chatobj compatibility)."""
        try:
            result = self.loop.run_until_complete(self.process_query(message))
            return result.get('final_text', 'No response generated')
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return f"Error: {str(e)}"

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        try:
            self.loop.run_until_complete(self.cleanup())
        except Exception as e:
            logger.error(f"Error in destructor: {e}")
        
        try:
            self.loop.close()
        except Exception as e:
            logger.error(f"Error closing event loop: {e}")
