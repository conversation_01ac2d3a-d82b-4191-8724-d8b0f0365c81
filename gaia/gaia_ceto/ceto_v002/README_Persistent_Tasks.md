# Persistent Long-Running Tasks for chat_term

This document explains how to implement and use persistent long-running tasks that survive client disconnections and allow reconnection to monitor progress.

## 🎯 Problem Statement

The original `long_task` tool in chat_term has a limitation: **when the client disconnects, the task stops**. This happens because:

1. The task execution depends on an active MCP client connection
2. Progress reporting (`ctx.report_progress()`) requires a connected client
3. When chat_term is closed, the connection is lost and the task is interrupted

## 💡 Solution: Persistent Task Architecture

We implement a **client-server pattern** where tasks run independently of client connections:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   chat_term     │    │ PersistentTask   │    │   SQLite DB     │
│   (Client)      │◄──►│   Manager        │◄──►│  (Storage)      │
│                 │    │  (Server)        │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🏗️ Key Components

### 1. PersistentTaskManager Class
- **Database Storage**: Uses SQLite to persist task state, progress, and messages
- **Background Execution**: Runs tasks as asyncio tasks independent of client connections
- **State Management**: Tracks task lifecycle (created → running → completed/failed)

### 2. Task Lifecycle
```python
# 1. Create task record in database
job_id = task_manager.create_task("long_task", metadata)

# 2. Start background execution
asyncio.create_task(task_manager.start_long_task(job_id, ctx))

# 3. Task runs independently, updating database
for i in range(5):
    await asyncio.sleep(2)  # Simulate work
    task_manager.update_task_status(job_id, "running", progress=i+1)
    task_manager.add_task_message(job_id, f"Step {i+1} completed")
```

## 📊 Demo Flow Analysis

### Phase 1: Task Creation & Initial Execution (0-3 seconds)

```
🧪 Persistent Task Demo
==================================================

1️⃣  Starting persistent long task...
ℹ️  🚀 Started persistent long task
ℹ️  📋 Job ID: 5958d055-b357-4aec-9ca8-49eb5645bece
ℹ️  💡 You can disconnect and reconnect using the job ID
Job ID: 5958d055-b357-4aec-9ca8-49eb5645bece
📊 [1/5] Progress: 20.0%
ℹ️  Completed step 1/5
```

**What happens:**
1. **Task Creation**: `create_task()` generates UUID and stores in SQLite database
2. **Background Start**: `asyncio.create_task()` starts the long-running task independently
3. **Initial Progress**: Task completes step 1 and reports progress to connected client
4. **Database Updates**: Each step updates SQLite with progress and messages

### Phase 2: Client Disconnection (3-5 seconds)

```
💔 Simulating client disconnection...
```

**What happens:**
1. **Client Disconnects**: `ctx.connected = False` simulates client going offline
2. **Task Continues**: The background asyncio task keeps running independently
3. **Database Persistence**: Task continues updating SQLite database
4. **No Client Updates**: Progress reports fail silently, but task doesn't stop

**Key Code:**
```python
# In start_long_task() - graceful handling of disconnection
if ctx and ctx.connected:
    try:
        await ctx.report_progress(step, 5)
        await ctx.info(f"Completed step {step}/5")
    except:
        # Client disconnected - task continues!
        ctx.connected = False
        self.add_task_message(job_id, "Client disconnected, continuing in background")
```

### Phase 3: Reconnection & Status Recovery (5 seconds)

```
🔄 Reconnecting to task...
ℹ️  📋 Reconnected to task: running
ℹ️  📊 Progress: 2/5
ℹ️  [2025-06-23T14:07:26] Starting long task with 5 steps...
ℹ️  [2025-06-23T14:07:28] Completed step 1/5
ℹ️  [2025-06-23T14:07:30] Completed step 2/5
📊 [2/5] Progress: 40.0%
ℹ️  🔄 Task is still running...
```

**What happens:**
1. **New Client Context**: Creates `new_ctx = Context("client_2")` (different client!)
2. **Database Query**: `get_task_status()` retrieves current state from SQLite
3. **Status Replay**: Shows current progress and recent messages from database
4. **Live Updates**: Since task is still running, shows current progress bar

### Phase 4: Final Status Check (10 seconds)

```
📊 Final status check...
ℹ️  📋 Reconnected to task: running
ℹ️  📊 Progress: 4/5
ℹ️  [2025-06-23T14:07:26] Starting long task with 5 steps...
ℹ️  [2025-06-23T14:07:28] Completed step 1/5
ℹ️  [2025-06-23T14:07:30] Completed step 2/5
ℹ️  [2025-06-23T14:07:32] Completed step 3/5
ℹ️  [2025-06-23T14:07:34] Completed step 4/5
📊 [4/5] Progress: 80.0%
ℹ️  🔄 Task is still running...
```

**What happens:**
1. **Progress Update**: Task has continued running and is now at step 4/5
2. **Message History**: Shows all messages from database (steps 1-4 completed)
3. **Still Running**: Task hasn't finished yet, so status is still "running"

## 🔑 Key Technical Insights

### 1. Dual Storage Strategy
```python
# In-memory for active tasks
self.active_tasks[job_id] = asyncio_task

# Persistent storage for state
self.update_task_status(job_id, "running", progress=step)
self.add_task_message(job_id, f"Completed step {step}/5")
```

### 2. Client Independence
```python
# Task runs independently of client connection
for i in range(5):
    await asyncio.sleep(2)  # Work continues regardless of client
    
    # Try to update client, but don't fail if disconnected
    if ctx and ctx.connected:
        try:
            await ctx.report_progress(step, 5)
        except:
            ctx.connected = False  # Mark as disconnected, continue task
```

### 3. State Recovery on Reconnection
```python
# Reconnection pulls state from database, not memory
task_info = self.get_task_status(job_id)  # SQLite query
await ctx.info(f"📊 Progress: {task_info['progress']}/{task_info['total']}")

# Show message history from database
for msg in task_info['recent_messages'][-5:]:
    await ctx.info(f"[{msg['timestamp'][:19]}] {msg['message']}")
```

## 🚀 Usage Examples

### Basic Usage
```bash
# Start the enhanced chat terminal
python chat_term_persistent.py --llm mock

# In chat_term:
/start_persistent long_task
# Output: Job ID: abc123...

# Close chat_term (Ctrl+C)
# Restart chat_term later

# Reconnect to the task
/reconnect abc123...
# Shows current progress and message history
```

### Available Commands
- `/start_persistent` - Start a persistent task that survives disconnection
- `/reconnect <job_id>` - Reconnect to a running task
- `/list_tasks` - List all active tasks
- `/task_status <job_id>` - Check detailed task status

## 📁 Files

- `persistent_task_example.py` - Core SQLite-based implementation
- `redis_task_manager.py` - Redis-based implementation for production
- `chat_term_persistent.py` - Enhanced chat_term with persistent task support

## 🎯 Why This Works

1. **Separation of Concerns**: Task execution is separate from client communication
2. **Persistent State**: SQLite database survives process restarts and client disconnections
3. **Async Independence**: `asyncio.create_task()` creates truly independent background execution
4. **Graceful Degradation**: Client disconnection doesn't crash the task
5. **State Recovery**: Any client can reconnect using the job ID and get full status

This architecture enables **true persistent long-running tasks** that survive client disconnections, process restarts, and allow multiple clients to monitor the same task! 🚀

## 🔄 Next Steps

1. **Try the demo**: Run `python persistent_task_example.py`
2. **Use enhanced chat_term**: Try `python chat_term_persistent.py`
3. **For production**: Consider the Redis-based implementation for scalability
4. **Integration**: Add persistent task support to your existing MCP servers
