#!/usr/bin/env python3
"""
Simple test for Firecrawl hosted MCP server using existing chat_term infrastructure.

This test uses the same import paths and setup as chat_term.py to ensure compatibility.
"""

import asyncio
import os
import sys

# Set up the same path as chat_term.py
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the same way as chat_term.py
try:
    from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MC<PERSON>lient<PERSON>ib as MCPSSEClientLib
    MCP_SSE_AVAILABLE = True
    print("✅ MCP SSE client library loaded successfully")
except ImportError as e:
    MCP_SSE_AVAILABLE = False
    print(f"❌ MCP SSE client library not available: {e}")

try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MC<PERSON>lient<PERSON>ib as MCPHTTPClientLib
    MCP_HTTP_AVAILABLE = True
    print("✅ MCP HTTP client library loaded successfully")
except ImportError as e:
    MCP_HTTP_AVAILABLE = False
    print(f"❌ MCP HTTP client library not available: {e}")

from mcp_server_registry import MCPServerRegistry


async def test_firecrawl_connection():
    """Test connecting to Firecrawl hosted MCP server."""
    print("\n🧪 Testing Firecrawl Hosted MCP Connection")
    print("=" * 50)
    
    # Check API key
    api_key = os.environ.get('FIRECRAWL_API_KEY')
    if not api_key:
        print("❌ FIRECRAWL_API_KEY not set")
        print("   Please set it with: export FIRECRAWL_API_KEY=fc-your-api-key")
        return False
    
    print(f"✅ API key found: {api_key[:8]}...")
    
    # Load registry and get Firecrawl server info
    registry = MCPServerRegistry()
    server_info = registry.get_server("example_firecrawl_hosted")
    
    if not server_info:
        print("❌ Firecrawl server not found in registry")
        return False
    
    print(f"✅ Found server: {server_info.name}")
    
    # Resolve URL
    resolved_url = server_info.get_resolved_url()
    if not resolved_url:
        print("❌ Could not resolve server URL")
        return False
    
    print(f"✅ Resolved URL: {resolved_url[:60]}...")
    
    # Test connection based on available clients
    if server_info.protocol == "sse" and MCP_SSE_AVAILABLE:
        print("\n🔌 Testing SSE connection...")
        return await test_sse_connection(resolved_url)
    elif server_info.protocol == "http" and MCP_HTTP_AVAILABLE:
        print("\n🔌 Testing HTTP connection...")
        return await test_http_connection(resolved_url)
    else:
        print(f"❌ No suitable client available for protocol: {server_info.protocol}")
        return False


async def test_sse_connection(url: str):
    """Test SSE connection to Firecrawl."""
    try:
        client = MCPSSEClientLib(debug_callback=debug_callback)
        print(f"🔗 Connecting to SSE endpoint: {url}")
        
        success = await client.connect_to_server(url)
        if success:
            print("✅ Successfully connected to Firecrawl SSE endpoint!")
            
            # List available tools
            tools = getattr(client, 'available_tools', [])
            if tools:
                print(f"📋 Available tools ({len(tools)}):")
                for tool in tools[:5]:
                    tool_name = tool['name'] if isinstance(tool, dict) else str(tool)
                    print(f"   - {tool_name}")
                if len(tools) > 5:
                    print(f"   ... and {len(tools) - 5} more")
            
            # Test a simple tool call
            print("\n🔧 Testing firecrawl_scrape tool...")
            try:
                result = await client.call_tool("firecrawl_scrape", {
                    "url": "https://httpbin.org/json",
                    "formats": ["markdown"],
                    "onlyMainContent": True
                })
                
                if result:
                    print("✅ Tool call successful!")
                    result_str = str(result)[:200] + "..." if len(str(result)) > 200 else str(result)
                    print(f"   Result: {result_str}")
                else:
                    print("⚠️  Tool call returned empty result")
            
            except Exception as e:
                print(f"❌ Tool call failed: {e}")
            
            # Cleanup
            if hasattr(client, 'cleanup'):
                await client.cleanup()
            
            return True
        else:
            print("❌ Failed to connect to SSE endpoint")
            return False
            
    except Exception as e:
        print(f"❌ SSE connection error: {e}")
        return False


async def test_http_connection(url: str):
    """Test HTTP connection to Firecrawl."""
    try:
        client = MCPHTTPClientLib(debug_callback=debug_callback)
        print(f"🔗 Connecting to HTTP endpoint: {url}")
        
        success = await client.connect_to_server(url)
        if success:
            print("✅ Successfully connected to Firecrawl HTTP endpoint!")
            
            # List available tools
            tools = getattr(client, 'available_tools', [])
            if tools:
                print(f"📋 Available tools ({len(tools)}):")
                for tool in tools[:5]:
                    tool_name = tool['name'] if isinstance(tool, dict) else str(tool)
                    print(f"   - {tool_name}")
                if len(tools) > 5:
                    print(f"   ... and {len(tools) - 5} more")
            
            # Test a simple tool call
            print("\n🔧 Testing firecrawl_scrape tool...")
            try:
                result = await client.call_tool("firecrawl_scrape", {
                    "url": "https://httpbin.org/json",
                    "formats": ["markdown"],
                    "onlyMainContent": True
                })
                
                if result:
                    print("✅ Tool call successful!")
                    result_str = str(result)[:200] + "..." if len(str(result)) > 200 else str(result)
                    print(f"   Result: {result_str}")
                else:
                    print("⚠️  Tool call returned empty result")
            
            except Exception as e:
                print(f"❌ Tool call failed: {e}")
            
            # Cleanup
            if hasattr(client, 'cleanup'):
                await client.cleanup()
            
            return True
        else:
            print("❌ Failed to connect to HTTP endpoint")
            return False
            
    except Exception as e:
        print(f"❌ HTTP connection error: {e}")
        return False


def debug_callback(level: str, message: str, data=None):
    """Debug callback for MCP clients."""
    if level == "error":
        print(f"🔴 [ERROR] {message}")
    elif level == "warning":
        print(f"🟡 [WARNING] {message}")
    elif level == "info":
        print(f"🔵 [INFO] {message}")
    else:
        print(f"⚪ [DEBUG] {message}")


async def main():
    """Main test function."""
    print("🚀 Firecrawl Hosted MCP Server Test (Simple)")
    print("=" * 60)
    
    print(f"MCP SSE Available: {MCP_SSE_AVAILABLE}")
    print(f"MCP HTTP Available: {MCP_HTTP_AVAILABLE}")
    
    if not (MCP_SSE_AVAILABLE or MCP_HTTP_AVAILABLE):
        print("\n❌ No MCP client libraries available!")
        print("   Make sure you're running from the correct directory")
        print("   and that the MCP libraries are properly installed.")
        return 1
    
    success = await test_firecrawl_connection()
    
    if success:
        print("\n✅ Firecrawl hosted MCP server test completed successfully!")
        print("\n🎯 What this proves:")
        print("   • URL template resolution works")
        print("   • MCP client libraries are available")
        print("   • Can connect to third-party MCP servers")
        print("   • Firecrawl tools are accessible")
        return 0
    else:
        print("\n❌ Test failed. This could be due to:")
        print("   • Invalid API key")
        print("   • Network connectivity issues")
        print("   • Firecrawl service unavailable")
        print("   • MCP protocol compatibility issues")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
