{"description": "Enhanced MCP Server Configuration", "third_party_servers": {"firecrawl": {"enabled": true, "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse", "protocol": "sse", "description": "Firecrawl hosted MCP server", "namespace": "web", "environment_required": ["FIRECRAWL_API_KEY"]}, "demo_server": {"enabled": false, "url": "http://localhost:8001/mcp", "protocol": "http", "description": "Demo MCP server", "namespace": "demo"}}, "chat_term_usage": {"description": "Connect chat_term to this enhanced server", "command": "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp", "available_tools": ["echostring (local)", "echostring_table (local)", "long_task (local)", "server_status (local)", "list_all_tools (local)", "third_party_health (local)", "web.firecrawl_scrape (delegated)", "demo.some_tool (delegated)"]}}