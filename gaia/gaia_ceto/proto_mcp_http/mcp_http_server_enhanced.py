#!/usr/bin/env python3
"""
Enhanced MCP HTTP Server with Third-Party Delegation

This module implements an enhanced MCP server that provides its own tools
AND delegates to third-party MCP servers in a nested fashion. This allows
chat_term to connect to a single server on port 9000 but access tools from
multiple sources.

Features:
- Local tools (echostring, long_task, etc.)
- Third-party MCP server delegation (Firecrawl, etc.)
- Namespaced tool access (e.g., web.firecrawl_scrape)
- Configuration-driven backend setup
- Maintains HTTP streaming paradigm
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, List, Optional, Any
from contextlib import AsyncExitStack

from mcp.server.fastmcp import FastMCP, Context
import argparse
import uvicorn

# Import shared tools from the common module
from gaia.gaia_ceto.proto_mcp.mcp_tools import (
    echostring,
    echostring_table,
    long_task,
    firecrawl_scrape_text_only,
)

# Import multi-MCP client for third-party delegation
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
try:
    from multi_mcp_client import MultiMCPClient
except ImportError:
    print("Warning: multi_mcp_client not found. Third-party delegation will be disabled.")
    MultiMCPClient = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedMCPServer:
    """Enhanced MCP server with local tools and third-party delegation."""
    
    def __init__(self, config_file: str = "server_config.json"):
        """Initialize the enhanced MCP server.
        
        Args:
            config_file: Path to configuration file for third-party servers
        """
        self.config_file = config_file
        self.mcp = FastMCP("gaia_enhanced_mcp_server")
        self.third_party_client = MultiMCPClient() if MultiMCPClient else None
        self.third_party_tools: Dict[str, Dict[str, Any]] = {}
        
        # Register local tools first
        self._register_local_tools()
    
    def _register_local_tools(self):
        """Register the local/native tools."""
        logger.info("Registering local tools...")
        
        # Register existing local tools
        self.mcp.add_tool(echostring)
        self.mcp.add_tool(echostring_table)
        self.mcp.add_tool(long_task)
        self.mcp.add_tool(firecrawl_scrape_text_only)
        
        # Add server management tools
        self._add_server_management_tools()
        
        logger.info("Local tools registered successfully")
    
    def _add_server_management_tools(self):
        """Add server management and status tools."""
        
        @self.mcp.tool()
        async def server_status(ctx: Context) -> str:
            """Get status of the enhanced MCP server and all third-party connections."""
            result = "Enhanced MCP Server Status\n"
            result += "=" * 35 + "\n\n"
            
            # Local tools count
            local_tool_count = len([name for name in dir(self.mcp) if not name.startswith('_')])
            result += f"Local Tools: {local_tool_count}\n"
            
            # Third-party connections
            if self.third_party_client:
                connections = self.third_party_client.list_servers()
                result += f"Third-party Servers: {len(connections)}\n"
                result += f"Third-party Tools: {len(self.third_party_tools)}\n\n"
                
                for server_id, info in connections.items():
                    result += f"{server_id}:\n"
                    result += f"  URL: {info['url']}\n"
                    result += f"  Protocol: {info['protocol']}\n"
                    result += f"  Tools: {len(info['tools'])}\n"
                    result += f"  Connected: {info['connected_at']}\n\n"
            else:
                result += "Third-party delegation: Disabled\n"
            
            return result
        
        @self.mcp.tool()
        async def list_all_tools(ctx: Context) -> str:
            """List all available tools (local and third-party)."""
            result = "All Available Tools\n"
            result += "=" * 25 + "\n\n"
            
            result += "Local Tools:\n"
            result += "-" * 15 + "\n"
            # This is a simplified list - in practice you'd enumerate actual tools
            local_tools = ["echostring", "echostring_table", "long_task", "firecrawl_scrape_text_only", "server_status", "list_all_tools"]
            for tool in local_tools:
                result += f"  {tool}\n"
            
            if self.third_party_tools:
                result += "\nThird-party Tools:\n"
                result += "-" * 20 + "\n"
                for tool_name in sorted(self.third_party_tools.keys()):
                    server_id = self.third_party_tools[tool_name]['server_id']
                    result += f"  {tool_name} (from {server_id})\n"
            
            return result
        
        @self.mcp.tool()
        async def third_party_health(ctx: Context) -> str:
            """Check health of all third-party server connections."""
            if not self.third_party_client:
                return "Third-party delegation is disabled"
            
            result = "Third-party Server Health\n"
            result += "=" * 30 + "\n\n"
            
            servers = self.third_party_client.list_servers()
            for server_id, info in servers.items():
                try:
                    # Try a simple tool call to test connectivity
                    test_tools = ['echostring', 'health_check', 'ping']
                    test_tool = None
                    
                    for tool_name in test_tools:
                        if tool_name in info['tools']:
                            test_tool = tool_name
                            break
                    
                    if test_tool:
                        test_result = await self.third_party_client.call_tool(
                            server_id=server_id,
                            tool_name=test_tool,
                            tool_input="health_test",
                            tool_call_id="health_check"
                        )
                        status = "✓ Healthy" if test_result['success'] else f"✗ Error: {test_result['error']}"
                    else:
                        status = "? No test tool available"
                        
                except Exception as e:
                    status = f"✗ Connection error: {e}"
                
                result += f"{server_id}: {status}\n"
            
            return result
    
    async def load_third_party_servers(self) -> bool:
        """Load and connect to third-party servers from config file."""
        if not self.third_party_client:
            logger.warning("Third-party client not available, skipping third-party server loading")
            return False
        
        if not os.path.exists(self.config_file):
            logger.info(f"No config file found at {self.config_file}, skipping third-party servers")
            return True  # Not an error, just no third-party servers
        
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file: {e}")
            return False
        
        third_party_servers = config.get('third_party_servers', {})
        if not third_party_servers:
            logger.info("No third-party servers defined in config")
            return True
        
        success_count = 0
        
        for server_id, server_config in third_party_servers.items():
            if not server_config.get('enabled', True):
                logger.info(f"Skipping disabled third-party server: {server_id}")
                continue
            
            url = server_config.get('url')
            protocol = server_config.get('protocol', 'http')
            description = server_config.get('description', '')
            namespace = server_config.get('namespace', server_id)
            
            if not url:
                logger.error(f"No URL specified for third-party server: {server_id}")
                continue
            
            # Handle environment variable substitution
            if '{' in url and '}' in url:
                try:
                    url = url.format(**os.environ)
                except KeyError as e:
                    logger.error(f"Environment variable not found for {server_id}: {e}")
                    continue
            
            # Connect to third-party server
            success = await self.third_party_client.add_server(
                server_id=server_id,
                server_url=url,
                protocol=protocol,
                description=description
            )
            
            if success:
                success_count += 1
                await self._register_third_party_tools(server_id, namespace)
        
        logger.info(f"Connected to {success_count}/{len(third_party_servers)} third-party servers")
        return success_count > 0 or len(third_party_servers) == 0
    
    async def _register_third_party_tools(self, server_id: str, namespace: str):
        """Register tools from a third-party server as delegated tools."""
        connection = self.third_party_client.connections.get(server_id)
        if not connection:
            return
        
        for tool in connection['tools']:
            original_tool_name = tool['name']
            namespaced_tool_name = f"{namespace}.{original_tool_name}"
            
            # Store tool mapping
            self.third_party_tools[namespaced_tool_name] = {
                'server_id': server_id,
                'original_name': original_tool_name,
                'schema': tool
            }
            
            # Create delegated tool function
            await self._create_delegated_tool(namespaced_tool_name, original_tool_name, server_id, tool)
    
    async def _create_delegated_tool(self, namespaced_name: str, original_name: str, 
                                   server_id: str, tool_schema: Dict[str, Any]):
        """Create a delegated tool that proxies to a third-party server."""
        
        async def delegated_tool_func(ctx: Context, **kwargs) -> str:
            """Delegated tool function that proxies to third-party server."""
            try:
                # Report progress
                await ctx.report_progress(
                    progress=0, 
                    total=3, 
                    message=f"Delegating {original_name} to {server_id}"
                )
                
                # Call the third-party tool
                result = await self.third_party_client.call_tool(
                    server_id=server_id,
                    tool_name=original_name,
                    tool_input=kwargs,
                    tool_call_id=f"delegated_{namespaced_name}"
                )
                
                await ctx.report_progress(
                    progress=2, 
                    total=3, 
                    message="Processing third-party response"
                )
                
                if result['success']:
                    await ctx.report_progress(
                        progress=3, 
                        total=3, 
                        message="Complete"
                    )
                    return result['content']
                else:
                    error_msg = f"Third-party error from {server_id}: {result['error']}"
                    logger.error(error_msg)
                    return error_msg
                    
            except Exception as e:
                error_msg = f"Delegation error calling {original_name} on {server_id}: {e}"
                logger.error(error_msg)
                return error_msg
        
        # Update tool schema for enhanced server
        enhanced_tool_schema = tool_schema.copy()
        enhanced_tool_schema['description'] = f"[{server_id}] {tool_schema['description']}"
        
        # Register the delegated tool with FastMCP
        self.mcp.add_tool(
            func=delegated_tool_func,
            name=namespaced_name,
            description=enhanced_tool_schema['description'],
            input_schema=enhanced_tool_schema['inputSchema']
        )
        
        logger.info(f"Registered delegated tool: {namespaced_name} -> {server_id}.{original_name}")
    
    async def initialize(self):
        """Initialize the server (load third-party servers)."""
        logger.info("Initializing enhanced MCP server...")

        # Load third-party servers
        await self.load_third_party_servers()

        logger.info("Enhanced MCP server initialization complete")

    def create_app(self):
        """Create the FastAPI app."""
        return self.mcp.streamable_http_app()

    def start_server(self, port: int = 9000, host: str = "0.0.0.0"):
        """Start the enhanced MCP server (synchronous)."""
        logger.info(f"Starting enhanced MCP server on {host}:{port}")

        # Create the app using streamable_http_app for HTTP streaming
        app = self.create_app()

        # Start the server
        uvicorn.run(app, host=host, port=port, log_level="info")
    
    async def cleanup(self):
        """Clean up third-party connections."""
        if self.third_party_client:
            await self.third_party_client.cleanup()


def create_example_server_config():
    """Create an example configuration file for third-party servers."""
    config = {
        "description": "Enhanced MCP Server Configuration",
        "third_party_servers": {
            "firecrawl": {
                "enabled": False,
                "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
                "protocol": "sse",
                "description": "Firecrawl hosted MCP server",
                "namespace": "web",
                "environment_required": ["FIRECRAWL_API_KEY"]
            },
            "demo_server": {
                "enabled": False,
                "url": "http://localhost:8001/mcp",
                "protocol": "http",
                "description": "Demo MCP server",
                "namespace": "demo"
            }
        },
        "chat_term_usage": {
            "description": "Connect chat_term to this enhanced server",
            "command": "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp",
            "available_tools": [
                "echostring (local)",
                "echostring_table (local)",
                "long_task (local)",
                "server_status (local)",
                "list_all_tools (local)",
                "third_party_health (local)",
                "web.firecrawl_scrape (delegated)",
                "demo.some_tool (delegated)"
            ]
        }
    }
    
    config_file = "server_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Created example server configuration: {config_file}")
    return config_file


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Enhanced MCP HTTP Server")
    parser.add_argument("--port", type=int, default=9000, help="HTTP port")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--config", default="server_config.json", help="Configuration file")
    parser.add_argument("--create-config", action="store_true", help="Create example config and exit")

    args = parser.parse_args()

    if args.create_config:
        create_example_server_config()
        return

    # Create the enhanced server
    enhanced_server = EnhancedMCPServer(args.config)

    # Initialize third-party servers asynchronously
    async def initialize_server():
        try:
            await enhanced_server.initialize()
        except Exception as e:
            logger.error(f"Failed to initialize server: {e}")
            return False
        return True

    # Run initialization
    try:
        success = asyncio.run(initialize_server())
        if not success:
            print("Failed to initialize enhanced server")
            return
    except Exception as e:
        print(f"Initialization error: {e}")
        return

    # Start the server (synchronous)
    try:
        enhanced_server.start_server(args.port, args.host)
    except KeyboardInterrupt:
        print("\nShutting down enhanced server...")
    except Exception as e:
        print(f"Server error: {e}")
    finally:
        # Cleanup third-party connections
        async def cleanup():
            await enhanced_server.cleanup()

        try:
            asyncio.run(cleanup())
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
        print("Enhanced server cleanup complete.")


if __name__ == "__main__":
    main()
