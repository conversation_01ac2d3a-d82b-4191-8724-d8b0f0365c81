#!/usr/bin/env python3
"""
Nested MCP Client Example

This script demonstrates how to connect to a nested MCP server that aggregates
multiple backend MCP servers. From the client's perspective, it's just a single
MCP server, but it provides access to tools from multiple backend servers.

Architecture:
    This Client -> Nested MCP Server -> Multiple Backend Servers

Benefits:
    - Simple single-server connection for clients
    - All backend tools available through one interface
    - Transparent load balancing and failover
    - Centralized tool discovery and management

Usage:
    python nested_mcp_client_example.py --server http://localhost:8080/mcp
"""

import asyncio
import logging
import os
import sys
from typing import Dict, List, Optional, Any

# Add the gaia path to import MCP client libraries
sys.path.append(os.path.join(os.path.dirname(__file__), 'gaia'))

try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
except ImportError as e:
    print(f"Error importing MCP client library: {e}")
    print("Make sure you're running this from the agbase_admin directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NestedMCPClientDemo:
    """Demo client for connecting to a nested MCP server."""
    
    def __init__(self, server_url: str = "http://localhost:8080/mcp"):
        """Initialize the demo client.
        
        Args:
            server_url: URL of the nested MCP server
        """
        self.server_url = server_url
        self.client = None
        
    def debug_callback(self, level: str, message: str, data: Any = None):
        """Debug callback for MCP client."""
        logger.log(getattr(logging, level.upper(), logging.INFO), f"MCP: {message}")
        if data and level == "debug":
            logger.debug(f"  Data: {data}")
    
    async def connect(self) -> bool:
        """Connect to the nested MCP server."""
        print(f"Connecting to nested MCP server at: {self.server_url}")
        
        self.client = MCPClientLib(debug_callback=self.debug_callback)
        
        success = await self.client.connect_to_server(self.server_url)
        
        if success:
            tool_names = [tool['name'] for tool in self.client.available_tools]
            print(f"Connected successfully! Available tools: {', '.join(tool_names)}")
            return True
        else:
            print("Failed to connect to nested MCP server")
            return False
    
    async def demo_tool_discovery(self):
        """Demonstrate tool discovery and backend server information."""
        print("\n" + "="*60)
        print("TOOL DISCOVERY DEMO")
        print("="*60)
        
        # List all available tools
        print(f"\nTotal tools available: {len(self.client.available_tools)}")
        for i, tool in enumerate(self.client.available_tools, 1):
            print(f"{i:2d}. {tool['name']}: {tool['description']}")
        
        # Use meta-tools to get backend server information
        print("\n" + "-"*40)
        print("BACKEND SERVER INFORMATION")
        print("-"*40)
        
        try:
            # List backend servers
            result = await self.client.call_tool(
                tool_name="list_backend_servers",
                tool_input={}
            )
            
            if result.success:
                print("\nBackend Servers:")
                print(result.content)
            else:
                print(f"Error getting backend servers: {result.error}")
        
        except Exception as e:
            print(f"Error calling list_backend_servers: {e}")
        
        try:
            # Get tool mapping
            result = await self.client.call_tool(
                tool_name="get_tool_mapping", 
                tool_input={}
            )
            
            if result.success:
                print("\nTool Mapping:")
                print(result.content)
            else:
                print(f"Error getting tool mapping: {result.error}")
                
        except Exception as e:
            print(f"Error calling get_tool_mapping: {e}")
    
    async def demo_backend_health(self):
        """Demonstrate backend server health checking."""
        print("\n" + "="*60)
        print("BACKEND HEALTH CHECK DEMO")
        print("="*60)
        
        try:
            result = await self.client.call_tool(
                tool_name="backend_server_health",
                tool_input={}
            )
            
            if result.success:
                print("\nBackend Server Health:")
                print(result.content)
            else:
                print(f"Error checking backend health: {result.error}")
                
        except Exception as e:
            print(f"Error calling backend_server_health: {e}")
    
    async def demo_tool_calls(self):
        """Demonstrate calling tools from different backend servers."""
        print("\n" + "="*60)
        print("TOOL CALLING DEMO")
        print("="*60)
        
        # Find some tools to test
        available_tools = [tool['name'] for tool in self.client.available_tools]
        
        # Test echostring tool (should be available from backend servers)
        if 'echostring' in available_tools:
            print("\nTesting echostring tool...")
            result = await self.client.call_tool(
                tool_name="echostring",
                tool_input="Hello from nested MCP client!"
            )
            
            if result.success:
                print(f"Result: {result.content}")
            else:
                print(f"Error: {result.error}")
        
        # Test prefixed tools (if any)
        prefixed_tools = [tool for tool in available_tools if '_' in tool and 
                         any(tool.startswith(prefix) for prefix in ['sse_', 'web_', 'gaia_'])]
        
        if prefixed_tools:
            print(f"\nTesting prefixed tool: {prefixed_tools[0]}")
            result = await self.client.call_tool(
                tool_name=prefixed_tools[0],
                tool_input="Testing prefixed tool call"
            )
            
            if result.success:
                print(f"Result: {result.content}")
            else:
                print(f"Error: {result.error}")
        
        # Test table tool if available
        table_tools = [tool for tool in available_tools if 'table' in tool.lower()]
        if table_tools:
            print(f"\nTesting table tool: {table_tools[0]}")
            result = await self.client.call_tool(
                tool_name=table_tools[0],
                tool_input={"items": ["Item 1", "Item 2", "Item 3"]}
            )
            
            if result.success:
                print(f"Result: {result.content}")
            else:
                print(f"Error: {result.error}")
    
    async def demo_llm_query(self):
        """Demonstrate using LLM with tools from the nested server."""
        print("\n" + "="*60)
        print("LLM QUERY DEMO")
        print("="*60)
        
        queries = [
            "List all the backend servers and their tools",
            "Echo the phrase 'Nested MCP is working great!' using available tools",
            "Check the health of all backend servers"
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"\nQuery {i}: {query}")
            print("-" * 50)
            
            try:
                result = await self.client.process_query(
                    query=query,
                    model="claude-3-5-sonnet-20240620",
                    max_tokens=1024,
                    tool_timeout=30
                )
                
                if result.get('error'):
                    print(f"Error: {result['error']}")
                else:
                    print(f"Response: {result.get('final_text', 'No response')}")
                    
                    # Show tool calls if any
                    tool_results = result.get('tool_results', [])
                    if tool_results:
                        print(f"Tools used: {len(tool_results)}")
                        for tool_result in tool_results:
                            if hasattr(tool_result, 'tool_name'):
                                print(f"  - {tool_result.tool_name}")
                            
            except Exception as e:
                print(f"Error processing query: {e}")
    
    async def interactive_mode(self):
        """Run an interactive demo mode."""
        print("\n" + "="*60)
        print("INTERACTIVE MODE")
        print("="*60)
        print("\nCommands:")
        print("  tools - List all available tools")
        print("  servers - List backend servers")
        print("  health - Check backend server health")
        print("  call <tool_name> <input> - Call a specific tool")
        print("  query <question> - Ask a question using LLM")
        print("  quit - Exit interactive mode")
        
        while True:
            try:
                command = input("\n> ").strip()
                
                if not command:
                    continue
                elif command == "quit":
                    break
                elif command == "tools":
                    for tool in self.client.available_tools:
                        print(f"  {tool['name']}: {tool['description']}")
                elif command == "servers":
                    result = await self.client.call_tool("list_backend_servers", {})
                    print(result.content if result.success else result.error)
                elif command == "health":
                    result = await self.client.call_tool("backend_server_health", {})
                    print(result.content if result.success else result.error)
                elif command.startswith("call "):
                    parts = command.split(" ", 2)
                    if len(parts) < 3:
                        print("Usage: call <tool_name> <input>")
                        continue
                    
                    tool_name = parts[1]
                    tool_input = parts[2]
                    
                    result = await self.client.call_tool(tool_name, tool_input)
                    print(result.content if result.success else result.error)
                    
                elif command.startswith("query "):
                    question = command[6:]  # Remove "query "
                    result = await self.client.process_query(question)
                    print(result.get('final_text', result.get('error', 'No response')))
                else:
                    print("Unknown command. Type 'quit' to exit.")
                    
            except KeyboardInterrupt:
                print("\nExiting interactive mode...")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    async def cleanup(self):
        """Clean up the client connection."""
        if self.client:
            await self.client.cleanup()


async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Nested MCP Client Demo")
    parser.add_argument("--server", default="http://localhost:8080/mcp", 
                       help="Nested MCP server URL")
    parser.add_argument("--interactive", action="store_true", 
                       help="Run in interactive mode")
    parser.add_argument("--demo", choices=["discovery", "health", "tools", "llm", "all"],
                       default="all", help="Which demo to run")
    
    args = parser.parse_args()
    
    # Create and connect the demo client
    demo = NestedMCPClientDemo(args.server)
    
    try:
        # Connect to the nested server
        success = await demo.connect()
        if not success:
            print("Failed to connect. Make sure the nested MCP server is running.")
            return
        
        # Run the requested demos
        if args.demo in ["discovery", "all"]:
            await demo.demo_tool_discovery()
        
        if args.demo in ["health", "all"]:
            await demo.demo_backend_health()
        
        if args.demo in ["tools", "all"]:
            await demo.demo_tool_calls()
        
        if args.demo in ["llm", "all"]:
            await demo.demo_llm_query()
        
        # Run interactive mode if requested
        if args.interactive:
            await demo.interactive_mode()
            
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await demo.cleanup()
        print("Demo complete!")


if __name__ == "__main__":
    asyncio.run(main())
