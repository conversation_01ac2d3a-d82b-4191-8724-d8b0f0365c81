#!/usr/bin/env python3
"""
Test Bridge Integration

This script tests the HTTP bridge server integration with the existing chat_term setup.
It verifies that the bridge server properly aggregates backend servers and maintains
compatibility with the HTTP streaming paradigm.

Usage:
    python test_bridge_integration.py --bridge-url http://localhost:8080/mcp
"""

import asyncio
import logging
import os
import sys
from typing import Dict, List, Optional, Any

# Add the gaia path to import MCP client libraries
sys.path.append(os.path.join(os.path.dirname(__file__), 'gaia'))

try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
except ImportError as e:
    print(f"Error importing MCP client library: {e}")
    print("Make sure you're running this from the agbase_admin directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BridgeIntegrationTest:
    """Test suite for HTTP bridge server integration."""
    
    def __init__(self, bridge_url: str = "http://localhost:8080/mcp"):
        """Initialize the test suite.
        
        Args:
            bridge_url: URL of the HTTP bridge server
        """
        self.bridge_url = bridge_url
        self.client = None
        self.test_results = []
        
    def debug_callback(self, level: str, message: str, data: Any = None):
        """Debug callback for MCP client."""
        logger.log(getattr(logging, level.upper(), logging.INFO), f"MCP: {message}")
    
    async def setup(self) -> bool:
        """Set up the test environment."""
        print(f"Setting up test environment...")
        print(f"Connecting to bridge server at: {self.bridge_url}")
        
        self.client = MCPClientLib(debug_callback=self.debug_callback)
        
        success = await self.client.connect_to_server(self.bridge_url)
        
        if success:
            tool_names = [tool['name'] for tool in self.client.available_tools]
            print(f"✓ Connected successfully! Available tools: {len(tool_names)}")
            return True
        else:
            print("✗ Failed to connect to bridge server")
            return False
    
    async def test_bridge_management_tools(self):
        """Test bridge-specific management tools."""
        print("\n" + "="*60)
        print("TESTING BRIDGE MANAGEMENT TOOLS")
        print("="*60)
        
        management_tools = ['bridge_status', 'bridge_tool_map', 'bridge_health_check']
        
        for tool_name in management_tools:
            try:
                print(f"\nTesting {tool_name}...")
                result = await self.client.call_tool(tool_name, {})
                
                if result.success:
                    print(f"✓ {tool_name} succeeded")
                    print(f"Response preview: {result.content[:200]}...")
                    self.test_results.append(f"✓ {tool_name}")
                else:
                    print(f"✗ {tool_name} failed: {result.error}")
                    self.test_results.append(f"✗ {tool_name}")
                    
            except Exception as e:
                print(f"✗ {tool_name} exception: {e}")
                self.test_results.append(f"✗ {tool_name} (exception)")
    
    async def test_namespaced_tools(self):
        """Test namespaced tools from backend servers."""
        print("\n" + "="*60)
        print("TESTING NAMESPACED BACKEND TOOLS")
        print("="*60)
        
        # Find namespaced tools
        available_tools = [tool['name'] for tool in self.client.available_tools]
        namespaced_tools = [tool for tool in available_tools if '.' in tool]
        
        print(f"Found {len(namespaced_tools)} namespaced tools:")
        for tool in namespaced_tools:
            print(f"  - {tool}")
        
        # Test some common namespaced tools
        test_tools = [
            ('gaia.echostring', 'Hello from Gaia HTTP backend!'),
            ('sse.echostring', 'Hello from SSE backend!'),
        ]
        
        for tool_name, test_input in test_tools:
            if tool_name in available_tools:
                try:
                    print(f"\nTesting {tool_name}...")
                    result = await self.client.call_tool(tool_name, test_input)
                    
                    if result.success:
                        print(f"✓ {tool_name} succeeded")
                        print(f"Response: {result.content}")
                        self.test_results.append(f"✓ {tool_name}")
                    else:
                        print(f"✗ {tool_name} failed: {result.error}")
                        self.test_results.append(f"✗ {tool_name}")
                        
                except Exception as e:
                    print(f"✗ {tool_name} exception: {e}")
                    self.test_results.append(f"✗ {tool_name} (exception)")
            else:
                print(f"⚠ {tool_name} not available (backend may not be running)")
                self.test_results.append(f"⚠ {tool_name} (not available)")
    
    async def test_progress_reporting(self):
        """Test progress reporting functionality."""
        print("\n" + "="*60)
        print("TESTING PROGRESS REPORTING")
        print("="*60)
        
        # Look for long-running tools that support progress reporting
        available_tools = [tool['name'] for tool in self.client.available_tools]
        progress_tools = [tool for tool in available_tools if 'long' in tool.lower() or 'task' in tool.lower()]
        
        if progress_tools:
            tool_name = progress_tools[0]
            print(f"Testing progress reporting with {tool_name}...")
            
            try:
                result = await self.client.call_tool(tool_name, {"duration": 5})
                
                if result.success:
                    print(f"✓ {tool_name} completed successfully")
                    print(f"Response: {result.content}")
                    self.test_results.append(f"✓ Progress reporting ({tool_name})")
                else:
                    print(f"✗ {tool_name} failed: {result.error}")
                    self.test_results.append(f"✗ Progress reporting ({tool_name})")
                    
            except Exception as e:
                print(f"✗ {tool_name} exception: {e}")
                self.test_results.append(f"✗ Progress reporting (exception)")
        else:
            print("⚠ No long-running tools found for progress testing")
            self.test_results.append("⚠ Progress reporting (no test tools)")
    
    async def test_llm_integration(self):
        """Test LLM integration with bridge tools."""
        print("\n" + "="*60)
        print("TESTING LLM INTEGRATION")
        print("="*60)
        
        test_queries = [
            "Get the bridge server status",
            "Echo 'Bridge integration test' using any available echo tool",
            "List the tool mapping for the bridge server"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\nTest Query {i}: {query}")
            print("-" * 50)
            
            try:
                result = await self.client.process_query(
                    query=query,
                    model="claude-3-5-sonnet-20240620",
                    max_tokens=1024,
                    tool_timeout=30
                )
                
                if result.get('error'):
                    print(f"✗ Query failed: {result['error']}")
                    self.test_results.append(f"✗ LLM Query {i}")
                else:
                    print(f"✓ Query succeeded")
                    print(f"Response: {result.get('final_text', 'No response')[:200]}...")
                    
                    # Show tool calls if any
                    tool_results = result.get('tool_results', [])
                    if tool_results:
                        print(f"Tools used: {len(tool_results)}")
                        for tool_result in tool_results:
                            if hasattr(tool_result, 'tool_name'):
                                print(f"  - {tool_result.tool_name}")
                    
                    self.test_results.append(f"✓ LLM Query {i}")
                    
            except Exception as e:
                print(f"✗ Query exception: {e}")
                self.test_results.append(f"✗ LLM Query {i} (exception)")
    
    async def test_chat_term_compatibility(self):
        """Test compatibility with chat_term patterns."""
        print("\n" + "="*60)
        print("TESTING CHAT_TERM COMPATIBILITY")
        print("="*60)
        
        # Test tool discovery (similar to what chat_term does)
        print("Testing tool discovery...")
        tools = self.client.available_tools
        print(f"✓ Discovered {len(tools)} tools")
        
        # Test tool schema validation
        print("Testing tool schema validation...")
        valid_schemas = 0
        for tool in tools:
            if all(key in tool for key in ['name', 'description', 'inputSchema']):
                valid_schemas += 1
        
        print(f"✓ {valid_schemas}/{len(tools)} tools have valid schemas")
        
        # Test connection health
        print("Testing connection health...")
        try:
            # Simple tool call to test connection
            simple_tools = [tool['name'] for tool in tools if 'bridge_status' in tool['name']]
            if simple_tools:
                result = await self.client.call_tool(simple_tools[0], {})
                if result.success:
                    print("✓ Connection health check passed")
                    self.test_results.append("✓ chat_term compatibility")
                else:
                    print("✗ Connection health check failed")
                    self.test_results.append("✗ chat_term compatibility")
            else:
                print("⚠ No suitable tools for health check")
                self.test_results.append("⚠ chat_term compatibility")
                
        except Exception as e:
            print(f"✗ Connection health check exception: {e}")
            self.test_results.append("✗ chat_term compatibility (exception)")
    
    def print_test_summary(self):
        """Print a summary of all test results."""
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        passed = len([r for r in self.test_results if r.startswith('✓')])
        failed = len([r for r in self.test_results if r.startswith('✗')])
        warnings = len([r for r in self.test_results if r.startswith('⚠')])
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Warnings: {warnings}")
        print()
        
        print("Detailed Results:")
        for result in self.test_results:
            print(f"  {result}")
        
        print()
        if failed == 0:
            print("🎉 All tests passed! Bridge server is ready for chat_term integration.")
        else:
            print("⚠️  Some tests failed. Check the bridge server configuration and backend connections.")
    
    async def cleanup(self):
        """Clean up test resources."""
        if self.client:
            await self.client.cleanup()


async def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test HTTP Bridge Server Integration")
    parser.add_argument("--bridge-url", default="http://localhost:8080/mcp",
                       help="HTTP bridge server URL")
    parser.add_argument("--test", choices=["all", "management", "tools", "progress", "llm", "compatibility"],
                       default="all", help="Which tests to run")
    
    args = parser.parse_args()
    
    # Create and run the test suite
    test_suite = BridgeIntegrationTest(args.bridge_url)
    
    try:
        # Setup
        success = await test_suite.setup()
        if not success:
            print("Failed to setup test environment. Make sure the bridge server is running.")
            return
        
        # Run tests
        if args.test in ["all", "management"]:
            await test_suite.test_bridge_management_tools()
        
        if args.test in ["all", "tools"]:
            await test_suite.test_namespaced_tools()
        
        if args.test in ["all", "progress"]:
            await test_suite.test_progress_reporting()
        
        if args.test in ["all", "llm"]:
            await test_suite.test_llm_integration()
        
        if args.test in ["all", "compatibility"]:
            await test_suite.test_chat_term_compatibility()
        
        # Print summary
        test_suite.print_test_summary()
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test error: {e}")
    finally:
        await test_suite.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
