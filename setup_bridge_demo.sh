#!/bin/bash
"""
Setup Bridge Demo

This script provides the correct commands to set up and test the HTTP bridge server
with your existing chat_term setup.

Usage:
    chmod +x setup_bridge_demo.sh
    ./setup_bridge_demo.sh
"""

echo "HTTP Bridge Server Setup Demo"
echo "============================="
echo ""

echo "Step 1: Start Backend Servers"
echo "------------------------------"
echo "In separate terminals, run these commands:"
echo ""
echo "# Terminal 1: Start Gaia HTTP server"
echo "cd gaia/gaia_ceto/proto_mcp_http"
echo "python mcp_http_server.py --port 9000"
echo ""
echo "# Terminal 2: Start Gaia SSE server (optional)"
echo "cd gaia/gaia_ceto/proto_mcp"
echo "python mcp_sse_server.py --port 9000"
echo ""

echo "Step 2: Create Bridge Configuration"
echo "-----------------------------------"
echo "python http_bridge_server.py --create-config"
echo ""

echo "Step 3: Start Bridge Server"
echo "---------------------------"
echo "python http_bridge_server.py --port 8080 --config bridge_config.json"
echo ""

echo "Step 4: Test Bridge Server"
echo "--------------------------"
echo "python test_bridge_integration.py --bridge-url http://localhost:8080/mcp"
echo ""

echo "Step 5: Connect chat_term to Bridge Server"
echo "------------------------------------------"
echo "# CORRECT COMMAND for chat_term:"
echo "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:8080/mcp"
echo ""

echo "Alternative chat_term commands:"
echo "------------------------------"
echo "# Use specific model with bridge server:"
echo "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:8080/mcp --model claude-3-5-sonnet-20240620"
echo ""
echo "# Connect directly to backend (bypass bridge):"
echo "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp"
echo ""

echo "Example chat_term Usage:"
echo "------------------------"
echo "Once connected, you can use these commands in chat_term:"
echo ""
echo "> Use gaia.echostring to echo \"Hello from Gaia backend\""
echo "> Use sse.echostring to echo \"Hello from SSE backend\""
echo "> Call bridge_status to see all connected backends"
echo "> Get bridge_tool_map to see the tool mapping"
echo "> Check bridge_health_check to verify backend health"
echo ""

echo "Troubleshooting:"
echo "----------------"
echo "If chat_term fails to connect:"
echo "1. Make sure the bridge server is running on port 8080"
echo "2. Check that backend servers are running and accessible"
echo "3. Verify the bridge server logs for connection errors"
echo "4. Test the bridge server directly with the test script"
echo ""

echo "Bridge Server Management:"
echo "-------------------------"
echo "# Check bridge server status"
echo "curl http://localhost:8080/health"
echo ""
echo "# View bridge server logs"
echo "# (Check the terminal where you started the bridge server)"
echo ""

echo "Configuration Files:"
echo "--------------------"
echo "# Bridge server configuration"
echo "cat bridge_config.json"
echo ""
echo "# Example mcp_servers.json entry (if using unified provider):"
echo 'cat << EOF'
echo '{'
echo '  "servers": {'
echo '    "http_bridge": {'
echo '      "name": "HTTP Bridge Server",'
echo '      "description": "Aggregated MCP server with all backend tools",'
echo '      "protocol": "http",'
echo '      "url": "http://0.0.0.0:8080/mcp",'
echo '      "type": "builtin",'
echo '      "tools": ["bridge_status", "gaia.echostring", "sse.echostring"],'
echo '      "model": "claude-3-5-sonnet-20240620"'
echo '    }'
echo '  }'
echo '}'
echo 'EOF'
echo ""

echo "Ready to start! Follow the steps above to set up the bridge server."
