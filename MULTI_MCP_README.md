# Multi-MCP Client Examples

This directory contains examples showing how to connect a single Python MCP client to multiple MCP servers simultaneously.

## Files

1. **`multi_mcp_client.py`** - Core multi-MCP client implementation
2. **`multi_mcp_config_example.py`** - Configuration-based client with interactive demo
3. **`MULTI_MCP_README.md`** - This documentation file

## Features

- **Multiple Server Support**: Connect to both HTTP and SSE MCP servers
- **Auto-routing**: Automatically find which server has a specific tool
- **Tool Aggregation**: Get all tools from all connected servers
- **Configuration-based**: Load server configurations from JSON files
- **Interactive Demo**: Command-line interface for testing

## Quick Start

### 1. Basic Multi-Server Connection

```python
from multi_mcp_client import MultiMCPClient

# Create client
client = MultiMCPClient()

# Add servers
await client.add_server("server1", "http://localhost:9000/mcp", "http")
await client.add_server("server2", "http://localhost:9001/mcp", "http")

# Call tools
result = await client.call_tool("server1", "echostring", "Hello!")
auto_result = await client.call_tool_auto("echostring", "Auto-routed!")

# Clean up
await client.cleanup()
```

### 2. Configuration-based Setup

```bash
# Run the config example (creates example config if none exists)
python multi_mcp_config_example.py

# Use custom config file
python multi_mcp_config_example.py my_servers.json
```

### 3. Example Configuration File

```json
{
  "description": "My MCP servers",
  "servers": {
    "gaia_local": {
      "enabled": true,
      "url": "http://0.0.0.0:9000/mcp",
      "protocol": "http",
      "description": "Local Gaia server"
    },
    "firecrawl": {
      "enabled": false,
      "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
      "protocol": "sse",
      "description": "Firecrawl hosted server"
    }
  }
}
```

## Key Classes and Methods

### MultiMCPClient

Main class for managing multiple MCP server connections.

**Methods:**
- `add_server(server_id, server_url, protocol, description)` - Connect to a server
- `call_tool(server_id, tool_name, tool_input)` - Call tool on specific server
- `call_tool_auto(tool_name, tool_input)` - Auto-route tool call
- `process_query(query, server_id)` - Process LLM query with tools
- `list_servers()` - Get info about connected servers
- `get_all_tools()` - Get all available tools
- `get_aggregated_tools()` - Get tools in Anthropic format
- `cleanup()` - Clean up all connections

### ConfigurableMultiMCPClient

Extended client that supports JSON configuration files.

**Additional Methods:**
- `load_from_config(config_file)` - Load servers from JSON config

## Usage Patterns

### 1. Tool Auto-routing

```python
# Instead of specifying which server has the tool
result = await client.call_tool("server1", "echostring", "Hello")

# Let the client find the right server automatically
result = await client.call_tool_auto("echostring", "Hello")
```

### 2. Server-specific Queries

```python
# Process query using a specific server
result = await client.process_query(
    "Echo 'Hello World'", 
    server_id="gaia_local"
)
```

### 3. Tool Discovery

```python
# List all servers and their tools
servers = client.list_servers()
for server_id, info in servers.items():
    print(f"{server_id}: {info['tools']}")

# Get aggregated tools for LLM use
all_tools = await client.get_aggregated_tools()
```

## Environment Variables

- `ANTHROPIC_API_KEY` - Required for LLM functionality
- `FIRECRAWL_API_KEY` - Required for Firecrawl servers (if used)

## Prerequisites

1. **MCP Client Libraries**: The examples require the MCP client libraries from the gaia project:
   - `gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib`
   - `gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib`

2. **Running MCP Servers**: You need actual MCP servers running. Examples:
   - Local Gaia server: `python mcp_http_server.py --port 9000`
   - Local SSE server: `python mcp_sse_server.py --port 9000`

3. **Python Dependencies**:
   - `asyncio`
   - `anthropic`
   - `mcp` (Model Context Protocol library)

## Error Handling

The client includes comprehensive error handling:

- Connection failures are logged and don't crash the client
- Tool calls return success/error status
- Missing tools are handled gracefully
- Environment variable substitution errors are caught

## Interactive Demo Commands

When running `multi_mcp_config_example.py`:

- `list` - Show all connected servers and tools
- `call <tool_name> <input>` - Call a tool with auto-routing
- `query <question>` - Ask a question using LLM + tools
- `quit` - Exit the demo

## Example Output

```
Loading configuration from: mcp_servers_config.json
Connected to 2/2 servers

Connected to 2 servers:
  gaia_http: Local Gaia MCP server (HTTP)
    Tools: echostring, echostring_table, long_task
  gaia_sse: Local Gaia MCP server (SSE)
    Tools: echostring, get_company_listing

> call echostring "Hello from multi-server client!"
Calling tool 'echostring' with input: Hello from multi-server client!
Result: Echo: Hello from multi-server client!
(Called on server: gaia_http)

> query "What tools are available?"
Processing query: What tools are available?
Response: I can see several tools available across the connected servers...
```

## Advanced Usage

### Custom Debug Callbacks

```python
def my_debug_callback(level, message, data=None):
    print(f"[{level.upper()}] {message}")
    if data:
        print(f"  Data: {data}")

client = MultiMCPClient()
client.debug_callback = my_debug_callback
```

### Connection Management

```python
# Check connection status
if "server1" in client.connections:
    info = client.connections["server1"]
    print(f"Connected at: {info['connected_at']}")
    print(f"Tools: {len(info['tools'])}")

# Manual cleanup of specific server
del client.connections["server1"]
```

This multi-server approach allows you to:
- Use specialized tools from different servers
- Distribute load across multiple servers
- Have fallback servers for redundancy
- Combine local and remote/hosted MCP servers
- Build more complex AI applications with diverse tool sets
