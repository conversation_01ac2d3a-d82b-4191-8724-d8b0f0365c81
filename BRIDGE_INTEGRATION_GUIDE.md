# HTTP Bridge Server Integration Guide

This guide shows how to integrate the HTTP Bridge Server with your existing `chat_term` setup, maintaining the HTTP streaming paradigm and all existing functionality.

## Architecture Overview

```
chat_term -> HTTP Bridge Server (Port 8080) -> Multiple Backend MCP Servers
```

The bridge server acts as a single HTTP MCP endpoint that aggregates multiple backend servers, following the same pattern as your original `bridge.py` but using HTTP transport instead of stdio.

## Quick Setup

### 1. Start Backend Servers

```bash
# Terminal 1: Start Gaia HTTP server
cd gaia/gaia_ceto/proto_mcp_http
python mcp_http_server.py --port 9000

# Terminal 2: Start Gaia SSE server (optional)
cd gaia/gaia_ceto/proto_mcp
python mcp_sse_server.py --port 9000

# Terminal 3: Start any other MCP servers you want to aggregate
```

### 2. Configure and Start Bridge Server

```bash
# Create configuration (if needed)
python http_bridge_server.py --create-config

# Edit bridge_config.json to match your setup
# Start the bridge server
python http_bridge_server.py --port 8080 --config bridge_config.json
```

### 3. Update mcp_servers.json for chat_term

Add the bridge server to your `gaia/gaia_ceto/ceto_v002/mcp_servers.json`:

```json
{
  "servers": {
    "http_bridge": {
      "name": "HTTP Bridge Server",
      "description": "Aggregated MCP server with all backend tools",
      "protocol": "http",
      "url": "http://0.0.0.0:8080/mcp",
      "type": "builtin",
      "health_check_path": "/health",
      "tools": [
        "bridge_status",
        "bridge_tool_map", 
        "bridge_health_check",
        "gaia.echostring",
        "gaia.echostring_table",
        "sse.echostring"
      ],
      "model": "claude-3-5-sonnet-20240620"
    }
  }
}
```

### 4. Connect chat_term to Bridge Server

```bash
# CORRECT COMMAND: Use the bridge server with chat_term
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:8080/mcp

# With specific model
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:8080/mcp --model claude-3-5-sonnet-20240620
```

## Configuration Details

### Bridge Server Configuration (`bridge_config.json`)

```json
{
  "description": "HTTP Bridge Server Configuration",
  "servers": {
    "gaia_http": {
      "enabled": true,
      "description": "Local Gaia MCP server (HTTP)",
      "transport": {
        "url": "http://0.0.0.0:9000/mcp",
        "protocol": "http"
      },
      "use_namespace": true,
      "namespace": "gaia"
    },
    "gaia_sse": {
      "enabled": true,
      "description": "Local Gaia MCP server (SSE)",
      "transport": {
        "url": "http://0.0.0.0:9000/sse",
        "protocol": "sse"
      },
      "use_namespace": true,
      "namespace": "sse"
    }
  }
}
```

### Namespacing Strategy

Following your `bridge.py` pattern, tools are namespaced as `{namespace}.{tool_name}`:

- `gaia.echostring` - echostring tool from Gaia HTTP server
- `sse.echostring` - echostring tool from Gaia SSE server
- `web.firecrawl_scrape` - firecrawl_scrape from Firecrawl server

## Key Features Maintained

### 1. HTTP Streaming Paradigm
- Uses FastMCP with HTTP transport
- Maintains streaming responses for long-running tasks
- Compatible with existing `mcp_http_clientlib`

### 2. Progress Reporting
- Bridge server forwards progress updates from backend servers
- `ctx.report_progress()` calls are preserved
- Real-time progress display in chat_term

### 3. Tool Discovery
- All backend tools are exposed through single endpoint
- Bridge management tools for monitoring and debugging
- Compatible with existing tool calling patterns

### 4. Error Handling
- Backend connection failures don't crash bridge server
- Tool call errors are properly propagated
- Health checking identifies problematic backends

## Bridge Management Tools

The bridge server automatically provides these management tools:

### `bridge_status`
Get status of all backend servers and tool mappings.

### `bridge_tool_map`
Get the complete tool mapping (bridge tool -> backend server).

### `bridge_health_check`
Perform health check on all backend servers.

## Usage Examples

### Direct Tool Calls

```python
# In chat_term or any MCP client
result = await client.call_tool("gaia.echostring", "Hello from bridge!")
result = await client.call_tool("sse.echostring", "Hello from SSE backend!")
result = await client.call_tool("bridge_status", {})
```

### LLM Queries

```
# In chat_term
> Check the status of all backend servers using bridge tools
> Echo "Hello World" using both gaia and sse servers
> Get the tool mapping for the bridge server
```

## Integration with Existing chat_term Commands

All existing chat_term functionality is preserved:

```bash
# Connect to bridge server
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:8080/mcp

# Connect to bridge server with specific model
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:8080/mcp --model claude-3-5-sonnet-20240620

# Connect directly to backend (bypass bridge)
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp

# Use SSE backend directly
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp --mcp-server http://localhost:9000/sse
```

## Comparison with Original bridge.py

### Similarities
- **Tool Namespacing**: Uses `{namespace}.{tool_name}` pattern
- **Backend Aggregation**: Connects to multiple backend servers
- **Single Interface**: Exposes all tools through one endpoint
- **Error Handling**: Robust error handling and logging

### Differences
- **Transport**: HTTP instead of stdio
- **Async Architecture**: Uses FastMCP for HTTP serving
- **Progress Reporting**: Built-in progress reporting support
- **Management Tools**: Additional bridge management tools
- **Configuration**: JSON-based configuration instead of hardcoded

## Deployment Scenarios

### Development Setup
```bash
# All on localhost with different ports
Backend 1: http://localhost:9000/mcp
Backend 2: http://localhost:9000/sse
Bridge:    http://localhost:8080/mcp
chat_term: connects to bridge
```

### Production Setup
```bash
# Distributed across different hosts
Backend 1: http://server1:9000/mcp
Backend 2: http://server2:9000/mcp
Bridge:    http://gateway:8080/mcp
chat_term: connects to gateway
```

## Troubleshooting

### Bridge Server Won't Start
1. Check if backend servers are running
2. Verify configuration file syntax
3. Check port availability
4. Review environment variables for third-party servers

### Tools Not Available
1. Use `bridge_status` to check backend connections
2. Use `bridge_tool_map` to verify tool registration
3. Check backend server logs
4. Verify namespacing configuration

### Performance Issues
1. Use `bridge_health_check` to identify slow backends
2. Check network connectivity to backend servers
3. Monitor bridge server logs for errors
4. Consider backend server load balancing

## Advanced Configuration

### Custom Namespacing
```json
{
  "servers": {
    "my_server": {
      "use_namespace": false,  // No namespace prefix
      "namespace": "custom"    // Custom prefix if use_namespace: true
    }
  }
}
```

### Environment Variables
```json
{
  "servers": {
    "api_server": {
      "transport": {
        "url": "https://api.example.com/{API_KEY}/mcp"
      },
      "environment_required": ["API_KEY"]
    }
  }
}
```

### Conditional Enabling
```json
{
  "servers": {
    "optional_server": {
      "enabled": false,  // Disable this backend
      "transport": {
        "url": "http://optional:9000/mcp"
      }
    }
  }
}
```

This bridge server approach maintains full compatibility with your existing chat_term setup while providing the benefits of backend server aggregation and centralized management.
