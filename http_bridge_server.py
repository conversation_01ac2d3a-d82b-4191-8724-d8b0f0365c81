#!/usr/bin/env python3
"""
HTTP Bridge Server for MCP

This server acts as an HTTP-based aggregator for multiple MCP backend servers,
maintaining compatibility with the existing HTTP streaming paradigm used by chat_term.

Architecture:
    chat_term -> HTTP Bridge Server -> Multiple Backend MCP Servers

Usage:
    python http_bridge_server.py --port 8080 --config bridge_config.json
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, Any, List, Optional
from contextlib import AsyncExitStack

# Add the gaia path to import MCP libraries
sys.path.append(os.path.join(os.path.dirname(__file__), 'gaia'))

try:
    from mcp.server.fastmcp import FastMCP, Context
    from mcp import types
    from multi_mcp_client import MultiMCPClient
except ImportError as e:
    print(f"Error importing required libraries: {e}")
    print("Make sure you're running this from the agbase_admin directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HTTPBridgeServer:
    """HTTP-based bridge server that aggregates multiple MCP backend servers."""
    
    def __init__(self, config_file: str = "bridge_config.json"):
        """Initialize the HTTP bridge server.
        
        Args:
            config_file: Path to configuration file defining backend servers
        """
        self.config_file = config_file
        self.backend_client = MultiMCPClient()
        self.server_map: Dict[str, str] = {}  # tool_name -> server_id
        self.tool_registry: Dict[str, Dict[str, Any]] = {}  # tool_name -> tool_schema
        self.mcp_server = FastMCP("http_bridge_server")
        
    async def load_backend_servers(self) -> bool:
        """Load and connect to backend servers from config file."""
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_file}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return False
        
        servers = config.get('servers', {})
        if not servers:
            logger.error("No servers defined in configuration")
            return False
        
        success_count = 0
        
        for server_id, server_config in servers.items():
            if not server_config.get('enabled', True):
                logger.info(f"Skipping disabled server: {server_id}")
                continue
            
            # Extract connection details
            transport = server_config.get('transport', {})
            url = transport.get('url')
            protocol = transport.get('protocol', 'http')
            description = server_config.get('description', '')
            
            if not url:
                logger.error(f"No URL specified for server: {server_id}")
                continue
            
            # Handle environment variable substitution
            if '{' in url and '}' in url:
                try:
                    url = url.format(**os.environ)
                except KeyError as e:
                    logger.error(f"Environment variable not found for {server_id}: {e}")
                    continue
            
            # Connect to backend server
            success = await self.backend_client.add_server(
                server_id=server_id,
                server_url=url,
                protocol=protocol,
                description=description
            )
            
            if success:
                success_count += 1
                await self._register_backend_tools(server_id, server_config)
        
        logger.info(f"Connected to {success_count}/{len(servers)} backend servers")
        return success_count > 0
    
    async def _register_backend_tools(self, server_id: str, server_config: Dict[str, Any]):
        """Register tools from a backend server with the bridge server."""
        connection = self.backend_client.connections.get(server_id)
        if not connection:
            return
        
        # Get namespacing configuration
        use_namespace = server_config.get('use_namespace', True)
        namespace = server_config.get('namespace', server_id)
        
        for tool in connection['tools']:
            original_tool_name = tool['name']
            
            # Create namespaced tool name following bridge.py pattern
            if use_namespace:
                bridge_tool_name = f"{namespace}.{original_tool_name}"
            else:
                bridge_tool_name = original_tool_name
            
            # Check for tool name conflicts
            if bridge_tool_name in self.server_map:
                logger.warning(f"Tool name conflict: {bridge_tool_name} exists on multiple servers")
                # Force namespace to resolve conflict
                bridge_tool_name = f"{server_id}.{original_tool_name}"
            
            # Map tool to server
            self.server_map[bridge_tool_name] = server_id
            self.tool_registry[bridge_tool_name] = {
                'original_name': original_tool_name,
                'server_id': server_id,
                'schema': tool
            }
            
            # Create the bridge tool function
            await self._create_bridge_tool(bridge_tool_name, original_tool_name, server_id, tool)
    
    async def _create_bridge_tool(self, bridge_tool_name: str, original_tool_name: str, 
                                 server_id: str, tool_schema: Dict[str, Any]):
        """Create a bridge tool that proxies to a backend server tool."""
        
        async def bridge_tool_func(ctx: Context, **kwargs) -> str:
            """Bridge tool function that proxies to backend server."""
            try:
                # Report progress for long-running tasks
                await ctx.report_progress(
                    progress=0, 
                    total=3, 
                    message=f"Routing {original_tool_name} to {server_id}"
                )
                
                # Call the backend tool
                result = await self.backend_client.call_tool(
                    server_id=server_id,
                    tool_name=original_tool_name,
                    tool_input=kwargs,
                    tool_call_id=f"bridge_{bridge_tool_name}"
                )
                
                await ctx.report_progress(
                    progress=2, 
                    total=3, 
                    message="Processing backend response"
                )
                
                if result['success']:
                    await ctx.report_progress(
                        progress=3, 
                        total=3, 
                        message="Complete"
                    )
                    return result['content']
                else:
                    error_msg = f"Backend error from {server_id}: {result['error']}"
                    logger.error(error_msg)
                    return error_msg
                    
            except Exception as e:
                error_msg = f"Bridge error calling {original_tool_name} on {server_id}: {e}"
                logger.error(error_msg)
                return error_msg
        
        # Update tool schema for bridge server
        bridge_tool_schema = tool_schema.copy()
        bridge_tool_schema['description'] = f"[{server_id}] {tool_schema['description']}"
        
        # Register the bridge tool with FastMCP
        self.mcp_server.add_tool(
            func=bridge_tool_func,
            name=bridge_tool_name,
            description=bridge_tool_schema['description'],
            input_schema=bridge_tool_schema['inputSchema']
        )
        
        logger.info(f"Registered bridge tool: {bridge_tool_name} -> {server_id}.{original_tool_name}")
    
    async def add_bridge_management_tools(self):
        """Add bridge server management tools."""
        
        @self.mcp_server.tool()
        async def bridge_status(ctx: Context) -> str:
            """Get status of all backend servers and tool mappings."""
            servers = self.backend_client.list_servers()
            
            result = "HTTP Bridge Server Status\n"
            result += "=" * 40 + "\n\n"
            
            result += f"Connected Backend Servers: {len(servers)}\n"
            result += f"Total Tools Available: {len(self.tool_registry)}\n\n"
            
            for server_id, info in servers.items():
                result += f"{server_id}:\n"
                result += f"  URL: {info['url']}\n"
                result += f"  Protocol: {info['protocol']}\n"
                result += f"  Description: {info['description']}\n"
                result += f"  Tools: {len(info['tools'])}\n"
                result += f"  Connected: {info['connected_at']}\n\n"
            
            return result
        
        @self.mcp_server.tool()
        async def bridge_tool_map(ctx: Context) -> str:
            """Get the complete tool mapping for the bridge server."""
            result = "Bridge Tool Mapping\n"
            result += "=" * 30 + "\n\n"
            
            for bridge_tool, details in self.tool_registry.items():
                server_id = details['server_id']
                original_name = details['original_name']
                result += f"{bridge_tool} -> {server_id}.{original_name}\n"
            
            return result
        
        @self.mcp_server.tool()
        async def bridge_health_check(ctx: Context) -> str:
            """Perform health check on all backend servers."""
            servers = self.backend_client.list_servers()
            
            result = "Backend Server Health Check\n"
            result += "=" * 35 + "\n\n"
            
            for server_id, info in servers.items():
                try:
                    # Find a simple tool to test connectivity
                    test_tools = ['echostring', 'health_check', 'ping']
                    test_tool = None
                    
                    for tool_name in test_tools:
                        if tool_name in info['tools']:
                            test_tool = tool_name
                            break
                    
                    if test_tool:
                        test_result = await self.backend_client.call_tool(
                            server_id=server_id,
                            tool_name=test_tool,
                            tool_input="bridge_health_test",
                            tool_call_id="health_check"
                        )
                        status = "✓ Healthy" if test_result['success'] else f"✗ Error: {test_result['error']}"
                    else:
                        status = "? No test tool available"
                        
                except Exception as e:
                    status = f"✗ Connection error: {e}"
                
                result += f"{server_id}: {status}\n"
            
            return result
    
    async def start_server(self, port: int = 8080, host: str = "0.0.0.0"):
        """Start the HTTP bridge server."""
        logger.info(f"Starting HTTP bridge server on {host}:{port}")
        
        # Load backend servers
        success = await self.load_backend_servers()
        if not success:
            logger.error("Failed to connect to backend servers")
            return False
        
        # Add bridge management tools
        await self.add_bridge_management_tools()
        
        # Start the FastMCP server with HTTP transport
        try:
            logger.info(f"Bridge server ready with {len(self.tool_registry)} tools from {len(self.backend_client.connections)} backends")
            await self.mcp_server.run(
                transport="http",
                http_port=port,
                http_host=host
            )
        except Exception as e:
            logger.error(f"Error starting bridge server: {e}")
            return False
        
        return True
    
    async def cleanup(self):
        """Clean up backend connections."""
        await self.backend_client.cleanup()


def create_example_bridge_config():
    """Create an example configuration file for the HTTP bridge server."""
    config = {
        "description": "HTTP Bridge Server Configuration - Compatible with chat_term",
        "bridge_server": {
            "name": "HTTP MCP Bridge",
            "description": "HTTP bridge aggregating multiple MCP backends",
            "port": 8080,
            "host": "0.0.0.0"
        },
        "servers": {
            "gaia_http": {
                "enabled": True,
                "description": "Local Gaia MCP server (HTTP)",
                "transport": {
                    "url": "http://0.0.0.0:9000/mcp",
                    "protocol": "http"
                },
                "use_namespace": True,
                "namespace": "gaia"
            },
            "gaia_sse": {
                "enabled": True,
                "description": "Local Gaia MCP server (SSE)",
                "transport": {
                    "url": "http://0.0.0.0:9000/sse",
                    "protocol": "sse"
                },
                "use_namespace": True,
                "namespace": "sse"
            },
            "firecrawl": {
                "enabled": False,
                "description": "Firecrawl hosted MCP server",
                "transport": {
                    "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
                    "protocol": "sse"
                },
                "use_namespace": True,
                "namespace": "web",
                "environment_required": ["FIRECRAWL_API_KEY"]
            },
            "demo_server": {
                "enabled": False,
                "description": "Demo MCP server",
                "transport": {
                    "url": "http://0.0.0.0:8001/mcp",
                    "protocol": "http"
                },
                "use_namespace": True,
                "namespace": "demo"
            }
        },
        "chat_term_integration": {
            "description": "To use with chat_term, add this server to mcp_servers.json",
            "example_entry": {
                "http_bridge": {
                    "name": "HTTP Bridge Server",
                    "description": "Aggregated MCP server with all backend tools",
                    "protocol": "http",
                    "url": "http://0.0.0.0:8080/mcp",
                    "type": "builtin",
                    "health_check_path": "/health",
                    "tools": ["bridge_status", "bridge_tool_map", "bridge_health_check", "gaia.echostring", "sse.echostring"],
                    "model": "claude-3-5-sonnet-20240620"
                }
            }
        }
    }
    
    config_file = "bridge_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Created example bridge server configuration: {config_file}")
    return config_file


async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="HTTP Bridge Server for MCP")
    parser.add_argument("--port", type=int, default=8080, help="Port to run on")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--config", default="bridge_config.json", help="Configuration file")
    parser.add_argument("--create-config", action="store_true", help="Create example config and exit")
    
    args = parser.parse_args()
    
    if args.create_config:
        create_example_bridge_config()
        return
    
    # Create example config if it doesn't exist
    if not os.path.exists(args.config):
        print(f"Configuration file not found: {args.config}")
        print("Creating example configuration...")
        create_example_bridge_config()
        print(f"Edit {args.config} to configure your backend servers, then run this script again.")
        return
    
    # Create and start the bridge server
    bridge_server = HTTPBridgeServer(args.config)
    
    try:
        await bridge_server.start_server(args.port, args.host)
    except KeyboardInterrupt:
        print("\nShutting down bridge server...")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await bridge_server.cleanup()
        print("Bridge server cleanup complete.")


if __name__ == "__main__":
    asyncio.run(main())
