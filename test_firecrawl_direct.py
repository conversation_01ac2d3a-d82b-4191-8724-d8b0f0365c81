#!/usr/bin/env python3
"""
Test Firecrawl MCP Server Direct Connection

This script tests the Firecrawl MCP server connection directly to isolate any issues.
"""

import asyncio
import logging
import os
import sys

# Add the gaia path to import MCP client libraries
sys.path.append(os.path.join(os.path.dirname(__file__), 'gaia'))

try:
    from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib
except ImportError as e:
    print(f"Error importing MCP client library: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def test_firecrawl_direct():
    """Test Firecrawl MCP server connection directly."""
    
    # Get API key
    api_key = os.getenv('FIRECRAWL_API_KEY')
    if not api_key:
        print("Error: FIRECRAWL_API_KEY environment variable not set")
        return
    
    # Construct URL
    firecrawl_url = f"https://mcp.firecrawl.dev/{api_key}/sse"
    print(f"Testing connection to: {firecrawl_url}")
    
    # Create client
    client = MCPClientLib(debug_callback=lambda level, msg, data=None: print(f"[{level.upper()}] {msg}"))
    
    try:
        # Connect
        print("Connecting to Firecrawl...")
        success = await client.connect_to_server(firecrawl_url)
        
        if not success:
            print("Failed to connect to Firecrawl")
            return
        
        print(f"Connected successfully! Available tools: {[tool['name'] for tool in client.available_tools]}")
        
        # Test a simple tool call
        print("\nTesting firecrawl_scrape tool...")
        
        test_input = {
            "url": "https://example.com",
            "formats": ["markdown"]
        }
        
        print(f"Calling firecrawl_scrape with input: {test_input}")
        
        result = await client.call_tool(
            tool_name="firecrawl_scrape",
            tool_input=test_input,
            tool_call_id="test_call"
        )
        
        print(f"Result success: {result.success}")
        if result.success:
            print(f"Content length: {len(result.content) if result.content else 0}")
            print(f"Content preview: {result.content[:200] if result.content else 'None'}...")
        else:
            print(f"Error: {result.error}")
            
    except Exception as e:
        print(f"Exception during test: {e}")
        logger.exception("Full exception details:")
        
    finally:
        # Cleanup
        try:
            await client.cleanup()
            print("Cleanup completed")
        except Exception as e:
            print(f"Cleanup error: {e}")


if __name__ == "__main__":
    asyncio.run(test_firecrawl_direct())
