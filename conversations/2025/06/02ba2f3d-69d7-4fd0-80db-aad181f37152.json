{"conversation_id": "02ba2f3d-69d7-4fd0-80db-aad181f37152", "user_id": "sam", "title": "Conversation 2025-06-26 15:53:16", "metadata": {}, "messages": [{"role": "user", "content": "web.firecrawl_scrape agfunder.com", "timestamp": "2025-06-26T15:53:22.241111"}, {"role": "assistant", "content": "Error: Error code: 400 - {'type': 'error', 'error': {'type': 'invalid_request_error', 'message': \"tools.7.custom.name: String should match pattern '^[a-zA-Z0-9_-]{1,128}$'\"}}", "timestamp": "2025-06-26T15:53:22.633032"}], "created_at": "2025-06-26T15:53:16.268032", "updated_at": "2025-06-26T15:53:22.633032"}